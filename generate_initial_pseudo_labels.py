import os
import torch
import numpy as np
import argparse
import json
from tqdm import tqdm
from segment_anything import sam_model_registry
from segment_anything.utils.transforms import ResizeLongestSide
from PIL import Image
import traceback

import torch.nn.functional as F # 引入 F
import cv2 # +++ 新增导入 +++

# ==================== FIDT图生成功能 ====================

def generate_fidt_map(binary_mask, alpha=0.02, beta=0.75, C=1):
    """
    生成FIDT（Fast Image Distance Transform）图

    基于公式: I = 1 / (P(x,y)^(α×P(x,y)+β) + C)
    其中 P(x,y) 是距离变换值

    Args:
        binary_mask: 二值化掩码 (numpy array, 0=背景, 1=前景)
        alpha: 超参数α，默认0.02
        beta: 超参数β，默认0.75
        C: 常数项，默认1

    Returns:
        fidt_map: FIDT图 (numpy array, float32)
    """
    # 确保输入是二值化的
    binary_mask = (binary_mask > 0.5).astype(np.uint8)

    # 背景距离计算：对前景掩码的反转版本进行距离变换
    # 得到每个像素到最近前景点的距离
    background_mask = 1 - binary_mask  # 背景=1，前景=0
    background_distances = cv2.distanceTransform(background_mask, cv2.DIST_L2, 5)

    # 前景距离计算：对前景掩码进行距离变换
    # 得到每个前景像素到最近背景的距离
    foreground_distances = cv2.distanceTransform(binary_mask, cv2.DIST_L2, 5)

    # 组合距离：前景区域使用前景距离，背景区域使用背景距离
    # 这样可以得到每个像素到边界的距离
    combined_distances = np.where(binary_mask > 0, foreground_distances, background_distances)

    # 避免除零错误，设置最小距离值
    P = np.maximum(combined_distances, 1e-6)

    # 计算FIDT值：I = 1 / (P(x,y)^(α×P(x,y)+β) + C)
    exponent = alpha * P + beta

    # 使用对数空间计算避免数值溢出
    # 原始计算：P^exponent 可能导致溢出，特别是当P很大时
    # 修复：使用 exp(exponent * log(P)) 进行等价计算
    log_power = exponent * np.log(P)

    # 限制log_power避免exp函数溢出
    # 使用保守的阈值确保数值稳定性：
    # exp(300) ≈ 1.9e130，远小于float64极限，确保不会溢出
    # 当power_term很大时，1/(power_term + C) ≈ 0，所以限制大值不会显著影响结果
    max_log_power = 300
    log_power_clipped = np.clip(log_power, None, max_log_power)

    # 计算P^exponent，现在数值稳定
    power_term = np.exp(log_power_clipped)
    denominator = power_term + C
    fidt_map = 1.0 / denominator

    return fidt_map.astype(np.float32)


# 注释：save_fidt_map 和 generate_and_save_fidt_for_boxes 函数已移除
# 因为现在FIDT图直接在 save_aggregated_data 函数中生成并聚合保存

# Ensure larch_dataset and utils are correctly importable
# Assuming they are in the same directory or PYTHONPATH
try:
    from larch_dataset import LarchDataset
except ImportError:
    print("错误: 无法导入 LarchDataset。请确保 larch_dataset.py 在 Python 路径中。")
    exit()
    
# 定义一个简化的set_seed函数，避免依赖utils
def set_seed(seed):
    """设置随机种子"""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

# +++ 新增辅助函数 +++
def generate_mask_from_box_prompt(sam_model, image_embedding, box_coords_original, original_image_size, resized_input_hw, sam_transform, device, image_name_for_debug="", morph_kernel_size=3, erosion_iterations=1, dilation_iterations=1): # ++ 添加形态学参数 ++
    try:
        print(f"[DEBUG {image_name_for_debug} - StageA] Box Coords Original: {box_coords_original}")
        box_coords_original_np = np.array([box_coords_original])
        
        transformed_boxes_np = sam_transform.apply_boxes(box_coords_original_np, original_image_size)
        print(f"[DEBUG {image_name_for_debug} - StageA] Transformed boxes NP: {transformed_boxes_np[0]}")

        box_prompt_torch = torch.as_tensor(transformed_boxes_np, dtype=torch.float, device=device)
        if box_prompt_torch.ndim == 2:
             box_prompt_torch = box_prompt_torch.unsqueeze(0)


        sparse_embeddings, dense_embeddings = sam_model.prompt_encoder(
            points=None,
            boxes=box_prompt_torch,
            masks=None
        )
        print(f"[DEBUG {image_name_for_debug} - StageA] Prompt Enc Sparse Emb: {sparse_embeddings.shape}, Dense Emb: {dense_embeddings.shape}")

        # ++ 修改为 multimask_output=True ++
        low_res_masks_multi, iou_predictions_multi = sam_model.mask_decoder(
            image_embeddings=image_embedding,
            image_pe=sam_model.prompt_encoder.get_dense_pe(),
            sparse_prompt_embeddings=sparse_embeddings,
            dense_prompt_embeddings=dense_embeddings,
            multimask_output=True, # ++ 改为 True ++
        )
        # print(f"[DEBUG {image_name_for_debug} - StageA] Low res masks multi shape: {low_res_masks_multi.shape}, All IoU preds: {iou_predictions_multi}")

        # ++ 选择最佳掩码 ++
        best_mask_idx = torch.argmax(iou_predictions_multi[0], dim=0)
        selected_low_res_mask = low_res_masks_multi[:, best_mask_idx:best_mask_idx+1, :, :] # 保持 [B, 1, H, W]
        
        print(f"[DEBUG {image_name_for_debug} - StageA] Selected mask index: {best_mask_idx.item()}, Chosen IoU: {iou_predictions_multi[0, best_mask_idx].item():.4f}")
        # print(f"[DEBUG {image_name_for_debug} - StageA] Selected low_res_mask for postproc shape: {selected_low_res_mask.shape}")

        full_res_mask_torch = sam_model.postprocess_masks(
            selected_low_res_mask, # ++ 使用选择的掩码 ++
            input_size=resized_input_hw,
            original_size=original_image_size
        )

        mask_np = (full_res_mask_torch[0, 0] > sam_model.mask_threshold).cpu().numpy().astype(np.uint8)
        print(f"[DEBUG {image_name_for_debug} - StageA] Output mask NP sum (before morph): {np.sum(mask_np)}")

        # +++ 应用形态学操作 +++
        if np.sum(mask_np) > 0: # 只对非空掩码操作
            kernel = np.ones((morph_kernel_size, morph_kernel_size), np.uint8)
            if erosion_iterations > 0:
                mask_np = cv2.erode(mask_np, kernel, iterations=erosion_iterations)
                print(f"[DEBUG {image_name_for_debug} - StageA] Mask NP sum (after erosion {erosion_iterations}x{morph_kernel_size}): {np.sum(mask_np)}")
            if dilation_iterations > 0:
                mask_np = cv2.dilate(mask_np, kernel, iterations=dilation_iterations)
                print(f"[DEBUG {image_name_for_debug} - StageA] Mask NP sum (after dilation {dilation_iterations}x{morph_kernel_size}): {np.sum(mask_np)}")
        # +++ 形态学操作结束 +++
        
        return mask_np, True
    except Exception as e:
        print(f"错误: Stage A (box prompt) for {image_name_for_debug} 失败: {e}")
        traceback.print_exc()
        return None, False

# 注释：save_individual_box_masks 函数已移除
# 因为现在统一使用 save_aggregated_data 函数进行聚合保存

# --- Stage B 函数已删除 ---
# def generate_mask_from_mask_prompt(...):
#     ...

# --- 辅助函数结束 ---

def parse_args():
    parser = argparse.ArgumentParser(description='生成初始伪标签 (单阶段, 基于框提示与形态学操作)') # 更新描述
    parser.add_argument('--data_root', type=str, default='/datadisk/FIDT-SAM/processed_larch_dataset',
                        help='处理后的数据集根目录')
    parser.add_argument('--sam_checkpoint', type=str, default='/datadisk/FIDT-SAM/sam_vit_h_4b8939.pth',
                        help='SAM模型检查点路径')
    parser.add_argument('--model_type', type=str, default='vit_h',
                        help='SAM模型类型')
    parser.add_argument('--batch_size', type=int, default=1, help='批处理大小(当前只支持1)')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载器的工作进程数')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--split', type=str, default='train', choices=['train', 'test'],
                        help="要生成伪标签的数据集分割 ('train' 或 'test')")

    # +++ 新增形态学参数 +++
    parser.add_argument('--morph_kernel_size', type=int, default=3, help='形态学操作的核大小')
    parser.add_argument('--erosion_iterations', type=int, default=1, help='腐蚀操作的迭代次数 (0表示不进行腐蚀)')
    parser.add_argument('--dilation_iterations', type=int, default=1, help='膨胀操作的迭代次数 (0表示不进行膨胀)')
    args = parser.parse_args()
    if args.batch_size != 1:
        print("警告: 当前实现只支持 batch_size=1。已强制设为 1。")
        args.batch_size = 1
    return args

def precompute_image_embeddings(args, sam, dataset, sam_transform, device):
    """
    阶段一：图像嵌入预计算与缓存
    """
    embeddings_dir = os.path.join(args.data_root, f'embeddings/{args.split}')
    os.makedirs(embeddings_dir, exist_ok=True)
    
    print(f"\n=== 阶段一：图像嵌入预计算 ===")
    print(f"缓存目录: {embeddings_dir}")
    
    # 检查是否需要重新计算嵌入
    total_images = len(dataset)
    existing_embeddings = set()
    
    # 扫描已存在的嵌入文件
    if os.path.exists(embeddings_dir):
        for filename in os.listdir(embeddings_dir):
            if filename.endswith('.pt'):
                base_name = os.path.splitext(filename)[0]
                existing_embeddings.add(base_name)
    
    print(f"发现 {len(existing_embeddings)} 个已存在的嵌入文件")
    
    # 创建需要处理的图像列表
    images_to_process = []
    for idx in range(total_images):
        try:
            sample = dataset[idx]
            if isinstance(sample, dict) and 'image_name' in sample:
                image_name = sample['image_name']
                if isinstance(image_name, list):
                    image_name = image_name[0]
                base_name = os.path.splitext(image_name)[0]
                
                if base_name not in existing_embeddings:
                    images_to_process.append((idx, image_name, base_name))
        except:
            continue
    
    if not images_to_process:
        print("✅ 所有图像嵌入已存在，跳过预计算阶段")
        return
    
    print(f"需要计算 {len(images_to_process)} 个图像的嵌入")
    
    # 计算嵌入
    with torch.no_grad():
        pbar = tqdm(images_to_process, desc="预计算图像嵌入")
        for idx, image_name, base_name in pbar:
            pbar.set_postfix({'image': image_name})
            
            try:
                sample = dataset[idx]
                if isinstance(sample, dict) and 'error' in sample:
                    print(f"跳过错误样本: {image_name}")
                    continue
                
                image_tensor = sample['image'].to(device)
                
                # 图像预处理
                if image_tensor.dim() == 4: 
                    image_tensor = image_tensor.squeeze(0)
                if image_tensor.is_sparse: 
                    image_tensor = image_tensor.to_dense()
                
                # 转换为SAM期望的格式
                if image_tensor.shape[0] == 3:  # CHW
                    input_image_np = image_tensor.permute(1, 2, 0).cpu().numpy() * 255.0
                elif image_tensor.shape[-1] == 3:  # HWC
                    input_image_np = image_tensor.cpu().numpy() * 255.0
                else:
                    print(f"跳过不支持的图像格式: {image_tensor.shape}")
                    continue
                
                # SAM变换和预处理
                input_image_resized = sam_transform.apply_image(input_image_np.astype(np.uint8))
                input_image_torch = torch.as_tensor(input_image_resized, dtype=torch.float32, device=device)
                input_image_torch = input_image_torch.permute(2, 0, 1).contiguous()
                input_image_preprocessed = sam.preprocess(input_image_torch.unsqueeze(0))
                
                # 计算嵌入
                image_embedding = sam.image_encoder(input_image_preprocessed)
                

                
                # 保存嵌入
                embedding_path = os.path.join(embeddings_dir, f"{base_name}.pt")
                torch.save(image_embedding.cpu(), embedding_path)
                
            except Exception as e:
                print(f"计算嵌入失败 {image_name}: {e}")
                continue
    
    print(f"✅ 图像嵌入预计算完成！保存到: {embeddings_dir}")

def save_aggregated_data(image_name, image_embedding, final_box_masks_dict,
                        original_boxes_by_class, original_size, output_dir, classes_list):
    """
    将图像嵌入、框、掩码和FIDT图聚合保存到单个.npz文件
    """
    base_name = os.path.splitext(image_name)[0]
    output_file = os.path.join(output_dir, f"{base_name}.npz")

    # 准备保存的数据
    save_data = {
        'image_name': image_name,
        'image_size': original_size,
        'image_embedding': image_embedding.cpu().numpy()
    }

    # 聚合所有框、掩码和FIDT图数据
    total_boxes = 0
    all_boxes = []
    all_masks = []
    all_fidt_maps = []  # 新增：存储FIDT图
    all_class_ids = []
    all_class_names = []

    print(f"  🎯 为图像 {image_name} 生成并聚合FIDT图...")

    for class_idx, class_name in enumerate(classes_list):
        boxes = original_boxes_by_class.get(class_name, [])
        masks = final_box_masks_dict.get(class_name, [])

        for box_idx, (box_coords, mask) in enumerate(zip(boxes, masks)):
            all_boxes.append(box_coords)
            all_masks.append(mask)
            all_class_ids.append(class_idx)
            all_class_names.append(class_name)

            # 生成对应的FIDT图
            try:
                # 确保掩码是二值化的numpy数组
                if torch.is_tensor(mask):
                    mask_np = mask.cpu().numpy()
                else:
                    mask_np = np.array(mask)

                # 生成FIDT图
                fidt_map = generate_fidt_map(mask_np, alpha=0.02, beta=0.75, C=1)
                all_fidt_maps.append(fidt_map)

            except Exception as e:
                print(f"  ❌ 生成FIDT图失败 (类别: {class_name}, 框: {total_boxes}): {e}")
                # 如果FIDT图生成失败，创建一个零图作为占位符
                fidt_map = np.zeros(original_size, dtype=np.float32)
                all_fidt_maps.append(fidt_map)

            total_boxes += 1

    if total_boxes > 0:
        save_data.update({
            'total_boxes': total_boxes,
            'boxes': np.array(all_boxes),  # [N, 4]
            'masks': np.array(all_masks),  # [N, H, W]
            'fidt_maps': np.array(all_fidt_maps),  # [N, H, W] 新增：FIDT图数组
            'class_ids': np.array(all_class_ids),  # [N]
            'class_names': all_class_names  # [N] 字符串列表
        })
        print(f"  ✅ FIDT图生成完成，共 {total_boxes} 个框")
    else:
        save_data.update({
            'total_boxes': 0,
            'boxes': np.empty((0, 4)),
            'masks': np.empty((0, original_size[0], original_size[1])),
            'fidt_maps': np.empty((0, original_size[0], original_size[1])),  # 新增：空FIDT图数组
            'class_ids': np.empty((0,), dtype=int),
            'class_names': []
        })

    # 使用压缩保存
    np.savez_compressed(output_file, **save_data)

    return total_boxes

def main():
    args = parse_args()
    set_seed(args.seed)
    
    # 设置输出目录 - 新的聚合格式
    output_dir = os.path.join(args.data_root, f"aggregated_dataset/{args.split}") 
    os.makedirs(output_dir, exist_ok=True)
    print(f"聚合数据集将保存在: {output_dir}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    print("加载 SAM 模型...")
    try:
        sam = sam_model_registry[args.model_type](checkpoint=args.sam_checkpoint)
        sam.to(device)
        sam.eval()
        print(f"SAM 模型 ({args.model_type}) 已从 {args.sam_checkpoint} 加载。")

    except Exception as e:
        print(f"错误: 加载 SAM 模型失败: {e}")
        traceback.print_exc()
        return
    
    try:
        with open(os.path.join(args.data_root, 'dataset_info.json'), 'r') as f:
            dataset_info = json.load(f)
            CLASSES = dataset_info['classes']
    except FileNotFoundError:
        print("错误: 未找到 dataset_info.json。请先运行 preprocess_larch_dataset.py")
        CLASSES = ['H', 'LD', 'HD', 'other'] 
        print("警告: 使用默认类别信息。")
    except Exception as e:
        print(f"错误: 加载 dataset_info.json 失败: {e}")
        return
    NUM_CLASSES = len(CLASSES)
    
    print("加载数据集...") # '加载训练数据集...' -> '加载数据集...'
    try:
        dataset = LarchDataset( # initial_dataset -> dataset
            args.data_root, 
            split=args.split
        )
        data_loader = torch.utils.data.DataLoader( # initial_loader -> data_loader
            dataset,
            batch_size=args.batch_size,  
            shuffle=False,  
            num_workers=args.num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )
    except Exception as e:
        print(f"错误: 加载数据集失败: {e}")
        traceback.print_exc()
        return
    
    sam_transform = ResizeLongestSide(sam.image_encoder.img_size)
    
    # 阶段一：预计算图像嵌入
    precompute_image_embeddings(args, sam, dataset, sam_transform, device)
    
    print("\n=== 阶段二：基于嵌入生成伪标签并聚合存储 ===")
    embeddings_dir = os.path.join(args.data_root, f'embeddings/{args.split}')
    
    total_images = len(dataset) 
    success_count = 0
    warning_count = 0
    initial_error_count = 0
    empty_masks_count = 0
    skipped_no_boxes_count = 0
    empty_mask_filenames = [] 
    error_details = [] 
    failed_image_indices = [] 
    
    with torch.no_grad():
        pbar = tqdm(enumerate(data_loader), desc=f"基于嵌入生成聚合数据集 ({args.split}集)", total=total_images)
        for idx, batch in pbar:
            image_info = None 
            try:
                if isinstance(batch, dict) and 'error' in batch:
                    image_info = batch.get('image_name', f'索引 {idx}') 
                    print(f"错误: DataLoader 返回错误，样本 {image_info}: {batch['error']}")
                    initial_error_count += 1
                    error_details.append({
                        'index': idx,
                        'image_name': image_info if isinstance(image_info, str) else '未知',
                        'error_type': 'DataLoaderError',
                        'message': batch['error'],
                        'traceback': batch.get('traceback', '')
                    })
                    if image_info != '未知': 
                         failed_image_indices.append((idx, image_info if isinstance(image_info, str) else f'未知_{idx}'))
                    continue

                image_tensor = batch['image'].to(device) 
                image_name = batch['image_name'] 
                
                if isinstance(image_name, list):
                    image_name = image_name[0]
                image_info = image_name 
                base_name = os.path.splitext(image_name)[0]
                
                has_any_box_for_any_class = False
                if 'boxes_by_class' in batch:
                    for cls_name_check in CLASSES:
                        if batch['boxes_by_class'].get(cls_name_check, torch.empty((0,4))).shape[0] > 0:
                            has_any_box_for_any_class = True
                            break
                
                if not has_any_box_for_any_class:
                    print(f"信息: 图像 {image_name} 对于所有目标类别 ({CLASSES}) 都没有边界框，跳过该图像。")
                    skipped_no_boxes_count += 1
                    continue
                
                pbar.set_postfix({'image': image_name})
                
                # 从预计算的嵌入加载图像嵌入
                base_name = os.path.splitext(image_name)[0]
                embedding_path = os.path.join(embeddings_dir, f"{base_name}.pt")
                
                try:
                    # 加载预计算的嵌入
                    if not os.path.exists(embedding_path):
                        print(f"警告: 嵌入文件不存在 {embedding_path}")
                        initial_error_count += 1
                        failed_image_indices.append((idx, image_name))
                        continue
                    
                    image_embedding = torch.load(embedding_path, map_location=device)
                    
                    # 获取原始图像尺寸
                    if image_tensor.dim() == 4: 
                        image_tensor = image_tensor.squeeze(0)
                    if image_tensor.is_sparse: 
                        image_tensor = image_tensor.to_dense()
                    
                    if image_tensor.shape[0] == 3:  # CHW
                        original_size = (image_tensor.shape[1], image_tensor.shape[2])
                        input_image_np_for_sam = image_tensor.permute(1, 2, 0).cpu().numpy() * 255.0
                    elif image_tensor.shape[-1] == 3:  # HWC
                        original_size = (image_tensor.shape[0], image_tensor.shape[1])
                        input_image_np_for_sam = image_tensor.cpu().numpy() * 255.0
                    else:
                        raise ValueError(f"不受支持的图像张量形状: {image_tensor.shape}")
                    
                    # 计算SAM变换后的尺寸（用于后续的框变换）
                    input_image_resized_np = sam_transform.apply_image(input_image_np_for_sam.astype(np.uint8))
                    resized_input_hw = input_image_resized_np.shape[:2]
                    
                except Exception as e:
                    print(f"错误: 加载嵌入失败 {image_name}: {e}")
                    traceback.print_exc()
                    initial_error_count += 1
                    error_details.append({
                        'index': idx,
                        'image_name': image_name,
                        'error_type': 'EmbeddingLoadError',
                        'message': str(e),
                        'traceback': traceback.format_exc()
                    })
                    failed_image_indices.append((idx, image_name)) 
                    continue

                if image_embedding is None or original_size is None:
                    initial_error_count += 1
                    error_details.append({
                        'index': idx,
                        'image_name': image_name,
                        'error_type': 'NoValidEmbeddingOrImageError',
                        'message': "无法获取有效的图像嵌入或处理图像NP数组",
                        'traceback': ""
                    })
                    failed_image_indices.append((idx, image_name)) 
                    continue

                # 新逻辑：收集每个框的掩码，而不是合并
                final_box_masks_dict = {}  # {class_name: [mask1, mask2, ...]}
                original_boxes_by_class = {}  # {class_name: [bbox1, bbox2, ...]}
                image_had_any_successful_mask = False
                image_had_processing_error = False
                
                for cls_idx, cls_name in enumerate(CLASSES):
                    final_box_masks_dict[cls_name] = []
                    original_boxes_by_class[cls_name] = []
                    
                    try:
                        all_boxes_for_class_tensor = batch['boxes_by_class'].get(cls_name, torch.empty((0, 4), device=device)).to(device) # 改为 (N,4)
                        
                        if all_boxes_for_class_tensor.dim() == 3 and all_boxes_for_class_tensor.shape[0] == 1: # [1, N, 4]
                            all_boxes_for_class_tensor = all_boxes_for_class_tensor.squeeze(0) # -> [N, 4]
                        
                        num_boxes_for_class = all_boxes_for_class_tensor.shape[0]

                        if num_boxes_for_class > 0:
                            for box_idx in range(num_boxes_for_class):
                                current_box_original_coords = all_boxes_for_class_tensor[box_idx, :].cpu().numpy() # [x1,y1,x2,y2]
                                debug_id = f"{image_name}_cls{cls_name}_box{box_idx}"

                                # STAGE A: 从框提示生成初始掩码
                                mask_from_box_np, success_A = generate_mask_from_box_prompt(
                                    sam_model=sam,
                                    image_embedding=image_embedding,
                                    box_coords_original=current_box_original_coords,
                                    original_image_size=original_size,
                                    resized_input_hw=resized_input_hw,
                                    sam_transform=sam_transform,
                                    device=device,
                                    image_name_for_debug=debug_id + "_StageA",
                                    morph_kernel_size=args.morph_kernel_size, # ++ 传递参数 ++
                                    erosion_iterations=args.erosion_iterations, # ++ 传递参数 ++
                                    dilation_iterations=args.dilation_iterations  # ++ 传递参数 ++
                                )
                                
                                final_single_box_mask_np = None
                                if success_A and mask_from_box_np is not None and np.sum(mask_from_box_np) > 0:
                                    final_single_box_mask_np = mask_from_box_np # 直接使用 Stage A 的结果
                                    # STAGE B 已移除
                                    # mask_from_mask_prompt_np, success_B = generate_mask_from_mask_prompt(...)
                                    # ...
                                else: # Stage A 失败或为空
                                    print(f"信息: {debug_id}_StageA 未成功或生成空掩码 (sum={np.sum(mask_from_box_np) if mask_from_box_np is not None else 'None'}).")
                                    # final_single_box_mask_np 保持为 None 或全零
                                
                                # 收集单个框的掩码
                                if final_single_box_mask_np is not None and final_single_box_mask_np.shape == original_size and np.sum(final_single_box_mask_np) > 0:
                                    final_box_masks_dict[cls_name].append(final_single_box_mask_np.astype(np.uint8))
                                    original_boxes_by_class[cls_name].append(current_box_original_coords)
                                    image_had_any_successful_mask = True
                                elif final_single_box_mask_np is not None and final_single_box_mask_np.shape != original_size:
                                    print(f"警告: {debug_id} 的最终掩码形状 {final_single_box_mask_np.shape} 与原始尺寸 {original_size} 不匹配")
                                    warning_count += 1

                    except Exception as e_class_outer:
                        print(f"错误: 准备处理类别 {cls_name} (图像: {image_name}) 时出错: {e_class_outer}")
                        traceback.print_exc()
                        warning_count +=1
                        image_had_processing_error = True
                        error_details.append({
                            'index': idx, 'image_name': image_name, 'class_name': cls_name,
                            'error_type': 'ClassDataSetupError', 'message': str(e_class_outer),
                            'traceback': traceback.format_exc()
                        })
            
                if image_had_any_successful_mask and not image_had_processing_error:
                    try:
                        # 使用新的聚合保存函数
                        total_boxes_saved = save_aggregated_data(
                            image_name=image_name,
                            image_embedding=image_embedding,
                            final_box_masks_dict=final_box_masks_dict,
                            original_boxes_by_class=original_boxes_by_class,
                            original_size=original_size,
                            output_dir=output_dir,
                            classes_list=CLASSES
                        )
                        success_count += 1
                        print(f"成功保存图像 {image_name} 的聚合数据 ({total_boxes_saved} 个框)")
                    except Exception as e_save:
                        print(f"错误: 保存聚合数据失败 {image_name}: {e_save}")
                        initial_error_count += 1
                        image_had_processing_error = True
                        error_details.append({
                            'index': idx,
                            'image_name': image_name, 
                            'error_type': 'AggregatedSaveError',
                            'message': str(e_save),
                            'traceback': traceback.format_exc()
                        })
                        if not any(fid[0] == idx and fid[1] == image_name for fid in failed_image_indices):
                            failed_image_indices.append((idx, image_name))
                
                if not image_had_any_successful_mask and not image_had_processing_error:
                    print(f"警告: 图像 {image_name} 未能生成任何有效掩码 (所有类别掩码为空)")
                    empty_masks_count += 1
                    empty_mask_filenames.append(image_name) 
                
                if image_had_processing_error:
                    print(f"错误: 图像 {image_name} 在处理中遇到错误，标记为失败。")
                    if not any(fid[0] == idx and fid[1] == image_name for fid in failed_image_indices):
                         failed_image_indices.append((idx, image_name))
            except Exception as e:
                image_name_in_error = image_info if image_info else f"索引 {idx}"
                print(f"错误: 处理批次失败 (图像: {image_name_in_error}): {e}")
                traceback.print_exc()
                initial_error_count += 1
                error_details.append({
                    'index': idx,
                    'image_name': image_name_in_error,
                    'error_type': 'BatchProcessingError',
                    'message': str(e),
                    'traceback': traceback.format_exc()
                })
                if image_name_in_error != f"索引 {idx}": 
                    failed_image_indices.append((idx, image_name_in_error))
                continue  

    print("\n--- 初始伪标签生成完成 ---") # 更新描述
    print(f"总图像数: {total_images}")
    print(f"成功生成掩码: {success_count}")
    print(f"没有生成有效掩码 (全空): {empty_masks_count}")
    print(f"处理警告: {warning_count}")
    print(f"初始处理错误: {initial_error_count}")
    print(f"因无任何类别的边界框而跳过的图像: {skipped_no_boxes_count}")

    if empty_mask_filenames:
        print("\n以下文件未能生成任何有效掩码 (全空):")
        for filename in empty_mask_filenames:
            print(f"  - {filename}")

    if error_details:
        print("\n以下文件在初始处理过程中遇到错误:")
        processed_errors = set()
        for error_item in error_details:
             error_key = (error_item['index'], error_item['image_name'])
             if error_key not in processed_errors:
                 print(f"  - 索引: {error_item['index']}, 文件: {error_item['image_name']}")
                 processed_errors.add(error_key)

    if failed_image_indices:
        print(f"\n--- 开始重试 {len(failed_image_indices)} 个失败的图像 ---") # 更新描述
        retry_success_count = 0
        retry_error_count = 0
        unique_failed_indices = list({item_idx: (item_idx, name) for item_idx, name in failed_image_indices}.values())

        retry_pbar = tqdm(unique_failed_indices, desc="重试失败图像")
        for idx, image_name in retry_pbar:
            retry_pbar.set_postfix({'image': image_name})
            try:
                # ... (数据加载和图像嵌入获取，与主循环类似) ...
                # --- START OF RETRY MASK GENERATION (UPDATED TO SINGLE-STAGE) ---
                
                batch_retry = dataset[idx] # 使用 dataset 重新获取数据
                if isinstance(batch_retry, dict) and 'error' in batch_retry:
                    print(f"  错误(重试数据加载): 无法加载 {image_name}: {batch_retry['error']}")
                    retry_error_count +=1
                    continue

                image_tensor_retry = batch_retry['image'].to(device)
                base_name_retry = os.path.splitext(image_name)[0]

                if image_tensor_retry.dim() == 4: image_tensor_retry = image_tensor_retry.squeeze(0)
                if image_tensor_retry.is_sparse: image_tensor_retry = image_tensor_retry.to_dense()
                
                input_image_np_for_sam_retry = None
                if image_tensor_retry.shape[0] == 3: # CHW
                    input_image_np_for_sam_retry = image_tensor_retry.permute(1, 2, 0).cpu().numpy() * 255.0
                elif image_tensor_retry.shape[-1] == 3: # HWC
                    input_image_np_for_sam_retry = image_tensor_retry.cpu().numpy() * 255.0
                else:
                    print(f"  警告(重试): 图像 {image_name} 张量形状 {image_tensor_retry.shape} 不受支持，跳过重试。")
                    retry_error_count +=1
                    continue
                
                original_height_retry, original_width_retry = input_image_np_for_sam_retry.shape[:2]
                original_size_retry = (original_height_retry, original_width_retry)

                input_image_resized_np_retry = sam_transform.apply_image(input_image_np_for_sam_retry.astype(np.uint8))
                resized_input_hw_retry = input_image_resized_np_retry.shape[:2]
                input_image_resized_torch_retry = torch.as_tensor(input_image_resized_np_retry, dtype=torch.float32, device=device).permute(2, 0, 1).contiguous()
                input_image_preprocessed_torch_retry = sam.preprocess(input_image_resized_torch_retry.unsqueeze(0))
                
                image_embedding_retry = None
                with torch.no_grad(): # 确保在 no_grad 上下文中
                    image_embedding_retry = sam.image_encoder(input_image_preprocessed_torch_retry)


                if image_embedding_retry is None:
                    print(f"  警告(重试): 图像 {image_name} 未能生成图像嵌入，跳过重试。")
                    retry_error_count +=1
                    continue

                # 重试逻辑：采用新的按框保存方案
                final_box_masks_dict_retry = {}  # {class_name: [mask1, mask2, ...]}
                original_boxes_by_class_retry = {}  # {class_name: [bbox1, bbox2, ...]}
                image_had_any_successful_mask_retry_flag = False

                for cls_idx_retry, cls_name_retry in enumerate(CLASSES):
                    final_box_masks_dict_retry[cls_name_retry] = []
                    original_boxes_by_class_retry[cls_name_retry] = []
                    
                    # 从 batch_retry 中获取特定类别的边界框
                    boxes_for_class_data = batch_retry.get('boxes_by_class', {})
                    all_boxes_for_class_tensor_retry = boxes_for_class_data.get(cls_name_retry, torch.empty((0, 4))).to(device) # 确保在正确设备上
                    
                    if all_boxes_for_class_tensor_retry.dim() == 3 and all_boxes_for_class_tensor_retry.shape[0] == 1:
                        all_boxes_for_class_tensor_retry = all_boxes_for_class_tensor_retry.squeeze(0)
                    
                    num_boxes_for_class_retry = all_boxes_for_class_tensor_retry.shape[0]

                    if num_boxes_for_class_retry > 0:
                        for box_idx_retry_loop in range(num_boxes_for_class_retry):
                            current_box_original_coords_retry = all_boxes_for_class_tensor_retry[box_idx_retry_loop, :].cpu().numpy()
                            debug_id_retry = f"{image_name}_cls{cls_name_retry}_box{box_idx_retry_loop}_retry"

                            # ++ 调用更新后的单阶段函数，并传递形态学参数 ++
                            mask_A_retry, success_A_retry = generate_mask_from_box_prompt(
                                sam_model=sam, # 使用全局 sam 模型
                                image_embedding=image_embedding_retry, 
                                box_coords_original=current_box_original_coords_retry,
                                original_image_size=original_size_retry, 
                                resized_input_hw=resized_input_hw_retry, 
                                sam_transform=sam_transform, 
                                device=device, 
                                image_name_for_debug=debug_id_retry + "_A",
                                morph_kernel_size=args.morph_kernel_size,
                                erosion_iterations=args.erosion_iterations,
                                dilation_iterations=args.dilation_iterations
                            )
                            
                            final_mask_retry_single_box = None
                            if success_A_retry and mask_A_retry is not None and np.sum(mask_A_retry) > 0:
                                final_mask_retry_single_box = mask_A_retry # 直接使用 Stage A 结果
                            else:
                                print(f"信息 (重试): {debug_id_retry}_StageA 未成功或生成空掩码.")
                            
                            if final_mask_retry_single_box is not None and final_mask_retry_single_box.shape == original_size_retry and np.sum(final_mask_retry_single_box) > 0:
                                final_box_masks_dict_retry[cls_name_retry].append(final_mask_retry_single_box.astype(np.uint8))
                                original_boxes_by_class_retry[cls_name_retry].append(current_box_original_coords_retry)
                        image_had_any_successful_mask_retry_flag = True

                if image_had_any_successful_mask_retry_flag:
                    try:
                        # 使用聚合保存函数，与主循环保持一致
                        total_boxes_saved_retry = save_aggregated_data(
                            image_name=image_name,
                            image_embedding=image_embedding_retry,
                            final_box_masks_dict=final_box_masks_dict_retry,
                            original_boxes_by_class=original_boxes_by_class_retry,
                            original_size=original_size_retry,
                            output_dir=output_dir,
                            classes_list=CLASSES
                        )
                        retry_success_count += 1
                        print(f"  成功(重试): 已保存图像 {image_name} 的聚合数据 ({total_boxes_saved_retry} 个框)")
                    except Exception as e_save_retry:
                        print(f"  错误(重试): 保存图像 {image_name} 失败: {e_save_retry}")
                else:
                    print(f"  警告(重试): 图像 {image_name} 重试后仍未生成有效掩码。")
                    # retry_error_count 不需要在这里增加，因为主循环的 initial_error_count 已经记录了它
                # --- END OF RETRY MASK GENERATION ---

            except Exception as e_main_retry:
                print(f"  错误: 重试图像 {image_name} (索引 {idx}) 时发生意外错误: {e_main_retry}")

        print(f"\n--- 重试阶段完成 ---")
        final_success_count = success_count + retry_success_count 
        final_error_count = initial_error_count - retry_success_count # This logic for error count might need review based on how errors are tracked now
        print("\n--- 最终统计结果 ---") # 更新描述
        print(f"总图像数: {total_images}")
        print(f"最终成功生成掩码: {final_success_count}")
        print(f"未能生成有效掩码 (全空): {empty_masks_count}")
        print(f"处理警告: {warning_count}")
        print(f"最终未能处理的错误: {final_error_count}")
        print(f"因无任何类别的边界框而跳过的图像(初始阶段): {skipped_no_boxes_count}")
    else:
        print("\n没有需要重试的图像。")
        print("\n--- 最终统计结果 ---") # 更新描述
        print(f"总图像数: {total_images}")
        print(f"成功生成掩码: {success_count}")
        print(f"没有生成有效掩码 (全空): {empty_masks_count}")
        print(f"处理警告: {warning_count}")
        print(f"处理错误: {initial_error_count}")
        print(f"因无任何类别的边界框而跳过的图像: {skipped_no_boxes_count}")

if __name__ == "__main__":
    main() 