聚合数据集将保存在: /datadisk/SAM/processed_larch_dataset/aggregated_dataset/train
使用设备: cuda
加载 SAM 模型...
SAM 模型 (vit_h) 已从 /datadisk/SAM/sam_vit_h_4b8939.pth 加载。
加载数据集...
[LarchDataset] 使用默认伪标签目录: /datadisk/SAM/processed_larch_dataset/train_pseudo_masks
[LarchDataset] 伪标签目录 /datadisk/SAM/processed_larch_dataset/train_pseudo_masks 不存在。请先运行伪标签生成脚本。

=== 阶段一：图像嵌入预计算 ===
缓存目录: /datadisk/SAM/processed_larch_dataset/embeddings/train
发现 667 个已存在的嵌入文件
✅ 所有图像嵌入已存在，跳过预计算阶段

=== 阶段二：基于嵌入生成伪标签并聚合存储 ===

基于嵌入生成聚合数据集 (train集):   0%|          | 0/667 [00:00<?, ?it/s]
基于嵌入生成聚合数据集 (train集):   0%|          | 0/667 [00:00<?, ?it/s, image=B01_0004.JPG][DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Box Coords Original: [1007.  146. 1183.  332.]
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Transformed boxes NP: [687.44533333  99.66933333 807.59466667 226.64533333]
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9612
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Output mask NP sum (before morph): 20709
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 14282
[DEBUG B01_0004.JPG_clsLD_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 20422
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Box Coords Original: [1029.    6. 1201.  140.]
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Transformed boxes NP: [702.464        4.096      819.88266667  95.57333333]
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9554
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Output mask NP sum (before morph): 18009
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 13503
[DEBUG B01_0004.JPG_clsLD_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 17863
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Box Coords Original: [1179.   84. 1339.  260.]
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Transformed boxes NP: [804.864       57.344      914.09066667 177.49333333]
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9557
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Output mask NP sum (before morph): 15050
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 9422
[DEBUG B01_0004.JPG_clsLD_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 14742
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Box Coords Original: [1335.  228. 1495.  386.]
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Transformed boxes NP: [ 911.36        155.648      1020.58666667  263.50933333]
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9563
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Output mask NP sum (before morph): 15450
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 9901
[DEBUG B01_0004.JPG_clsLD_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 15141
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Box Coords Original: [1137.  264. 1337.  466.]
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Transformed boxes NP: [776.192      180.224      912.72533333 318.12266667]
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9623
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Output mask NP sum (before morph): 22961
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Mask NP sum (after erosion 10x3): 16161
[DEBUG B01_0004.JPG_clsLD_box4_StageA - StageA] Mask NP sum (after dilation 10x3): 22741
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Box Coords Original: [1257.  408. 1439.  628.]
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Transformed boxes NP: [858.112      278.528      982.35733333 428.71466667]
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9532
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Output mask NP sum (before morph): 22860
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Mask NP sum (after erosion 10x3): 15588
[DEBUG B01_0004.JPG_clsLD_box5_StageA - StageA] Mask NP sum (after dilation 10x3): 22608
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Box Coords Original: [ 979.  356. 1119.  508.]
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Transformed boxes NP: [668.33066667 243.02933333 763.904      346.79466667]
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9373
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Output mask NP sum (before morph): 10370
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Mask NP sum (after erosion 10x3): 6043
[DEBUG B01_0004.JPG_clsLD_box6_StageA - StageA] Mask NP sum (after dilation 10x3): 10243
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Box Coords Original: [1117.  454. 1301.  690.]
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Transformed boxes NP: [762.53866667 309.93066667 888.14933333 471.04      ]
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9418
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Output mask NP sum (before morph): 21485
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Mask NP sum (after erosion 10x3): 14593
[DEBUG B01_0004.JPG_clsLD_box7_StageA - StageA] Mask NP sum (after dilation 10x3): 20973
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Box Coords Original: [839. 398. 997. 578.]
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Transformed boxes NP: [572.75733333 271.70133333 680.61866667 394.58133333]
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9598
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Output mask NP sum (before morph): 20341
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Mask NP sum (after erosion 10x3): 13967
[DEBUG B01_0004.JPG_clsLD_box8_StageA - StageA] Mask NP sum (after dilation 10x3): 20167
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Box Coords Original: [ 857.  516. 1181.  750.]
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Transformed boxes NP: [585.04533333 352.256      806.22933333 512.        ]
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9504
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Output mask NP sum (before morph): 31162
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Mask NP sum (after erosion 10x3): 21524
[DEBUG B01_0004.JPG_clsLD_box9_StageA - StageA] Mask NP sum (after dilation 10x3): 30284
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Box Coords Original: [1181.  626. 1383.  798.]
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Transformed boxes NP: [806.22933333 427.34933333 944.128      544.768     ]
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9474
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Output mask NP sum (before morph): 16665
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Mask NP sum (after erosion 10x3): 10888
[DEBUG B01_0004.JPG_clsLD_box10_StageA - StageA] Mask NP sum (after dilation 10x3): 16408
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Box Coords Original: [1173.  768. 1383. 1026.]
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Transformed boxes NP: [800.768 524.288 944.128 700.416]
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9385
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Output mask NP sum (before morph): 27582
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Mask NP sum (after erosion 10x3): 19862
[DEBUG B01_0004.JPG_clsLD_box11_StageA - StageA] Mask NP sum (after dilation 10x3): 27282
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Box Coords Original: [ 985.  768. 1183.  976.]
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Transformed boxes NP: [672.42666667 524.288      807.59466667 666.28266667]
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9407
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Output mask NP sum (before morph): 18929
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Mask NP sum (after erosion 10x3): 12806
[DEBUG B01_0004.JPG_clsLD_box12_StageA - StageA] Mask NP sum (after dilation 10x3): 18646
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Box Coords Original: [1307. 1060. 1483. 1256.]
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Transformed boxes NP: [ 892.24533333  723.62666667 1012.39466667  857.42933333]
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9512
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Output mask NP sum (before morph): 20105
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Mask NP sum (after erosion 10x3): 13792
[DEBUG B01_0004.JPG_clsLD_box13_StageA - StageA] Mask NP sum (after dilation 10x3): 19872
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Box Coords Original: [539. 518. 693. 642.]
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Transformed boxes NP: [367.95733333 353.62133333 473.088      438.272     ]
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9534
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Output mask NP sum (before morph): 12662
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Mask NP sum (after erosion 10x3): 7553
[DEBUG B01_0004.JPG_clsLD_box14_StageA - StageA] Mask NP sum (after dilation 10x3): 12493
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Box Coords Original: [395. 546. 537. 690.]
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Transformed boxes NP: [269.65333333 372.736      366.592      471.04      ]
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9372
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Output mask NP sum (before morph): 9284
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Mask NP sum (after erosion 10x3): 5121
[DEBUG B01_0004.JPG_clsLD_box15_StageA - StageA] Mask NP sum (after dilation 10x3): 9061
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Box Coords Original: [483. 640. 677. 804.]
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Transformed boxes NP: [329.728      436.90666667 462.16533333 548.864     ]
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9603
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Output mask NP sum (before morph): 18992
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Mask NP sum (after erosion 10x3): 13153
[DEBUG B01_0004.JPG_clsLD_box16_StageA - StageA] Mask NP sum (after dilation 10x3): 18913
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Box Coords Original: [511. 810. 699. 980.]
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Transformed boxes NP: [348.84266667 552.96       477.184      669.01333333]
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9548
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Output mask NP sum (before morph): 19506
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Mask NP sum (after erosion 10x3): 13710
[DEBUG B01_0004.JPG_clsLD_box17_StageA - StageA] Mask NP sum (after dilation 10x3): 19430
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Box Coords Original: [  35.  768.  227. 1052.]
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Transformed boxes NP: [ 23.89333333 524.288      154.96533333 718.16533333]
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9200
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Output mask NP sum (before morph): 24138
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Mask NP sum (after erosion 10x3): 15742
[DEBUG B01_0004.JPG_clsLD_box18_StageA - StageA] Mask NP sum (after dilation 10x3): 22862
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Box Coords Original: [ 105. 1046.  251. 1144.]
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Transformed boxes NP: [ 71.68       714.06933333 171.34933333 780.97066667]
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9479
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Output mask NP sum (before morph): 10528
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Mask NP sum (after erosion 10x3): 6200
[DEBUG B01_0004.JPG_clsLD_box19_StageA - StageA] Mask NP sum (after dilation 10x3): 10400
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Box Coords Original: [ 367.  996.  555. 1206.]
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Transformed boxes NP: [250.53866667 679.936      378.88       823.296     ]
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9548
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Output mask NP sum (before morph): 25543
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Mask NP sum (after erosion 10x3): 18037
[DEBUG B01_0004.JPG_clsLD_box20_StageA - StageA] Mask NP sum (after dilation 10x3): 25237
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Box Coords Original: [ 673.  902.  835. 1066.]
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Transformed boxes NP: [459.43466667 615.76533333 570.02666667 727.72266667]
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9645
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Output mask NP sum (before morph): 15247
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Mask NP sum (after erosion 10x3): 10034
[DEBUG B01_0004.JPG_clsLD_box21_StageA - StageA] Mask NP sum (after dilation 10x3): 15114
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Box Coords Original: [ 681. 1066.  823. 1204.]
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Transformed boxes NP: [464.896      727.72266667 561.83466667 821.93066667]
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9454
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Output mask NP sum (before morph): 10251
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Mask NP sum (after erosion 10x3): 5957
[DEBUG B01_0004.JPG_clsLD_box22_StageA - StageA] Mask NP sum (after dilation 10x3): 10137
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Box Coords Original: [ 165. 1326.  239. 1395.]
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Transformed boxes NP: [112.64       905.216      163.15733333 952.32      ]
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Selected mask index: 2, Chosen IoU: 0.9534
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Output mask NP sum (before morph): 5214
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Mask NP sum (after erosion 10x3): 2175
[DEBUG B01_0004.JPG_clsLD_box23_StageA - StageA] Mask NP sum (after dilation 10x3): 5095
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Box Coords Original: [ 611. 1213.  741. 1371.]
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Transformed boxes NP: [417.10933333 828.07466667 505.856      935.936     ]
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9706
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Output mask NP sum (before morph): 12587
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Mask NP sum (after erosion 10x3): 7921
[DEBUG B01_0004.JPG_clsLD_box24_StageA - StageA] Mask NP sum (after dilation 10x3): 12461
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Box Coords Original: [ 476. 1254.  580. 1366.]
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Transformed boxes NP: [324.94933333 856.064      395.94666667 932.52266667]
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9557
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Output mask NP sum (before morph): 7055
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Mask NP sum (after erosion 10x3): 3512
[DEBUG B01_0004.JPG_clsLD_box25_StageA - StageA] Mask NP sum (after dilation 10x3): 6932
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Box Coords Original: [1174. 1224. 1287. 1348.]
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Transformed boxes NP: [801.45066667 835.584      878.592      920.23466667]
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9569
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Output mask NP sum (before morph): 11151
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Mask NP sum (after erosion 10x3): 6719
[DEBUG B01_0004.JPG_clsLD_box26_StageA - StageA] Mask NP sum (after dilation 10x3): 10999
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Box Coords Original: [1201. 1349. 1313. 1469.]
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Transformed boxes NP: [ 819.88266667  920.91733333  896.34133333 1002.83733333]
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9565
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Output mask NP sum (before morph): 7177
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Mask NP sum (after erosion 10x3): 3549
[DEBUG B01_0004.JPG_clsLD_box27_StageA - StageA] Mask NP sum (after dilation 10x3): 7069
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Box Coords Original: [1342.  747. 1500.  912.]
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Transformed boxes NP: [ 916.13866667  509.952      1024.          622.592     ]
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9616
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Output mask NP sum (before morph): 16403
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Mask NP sum (after erosion 10x3): 11676
[DEBUG B01_0004.JPG_clsLD_box28_StageA - StageA] Mask NP sum (after dilation 10x3): 16276
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Box Coords Original: [1371.  559. 1500.  777.]
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Transformed boxes NP: [ 935.936       381.61066667 1024.          530.432     ]
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9576
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Output mask NP sum (before morph): 15147
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Mask NP sum (after erosion 10x3): 11228
[DEBUG B01_0004.JPG_clsLD_box29_StageA - StageA] Mask NP sum (after dilation 10x3): 14968
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Box Coords Original: [1325.   30. 1500.  234.]
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Transformed boxes NP: [ 904.53333333   20.48       1024.          159.744     ]
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9677
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Output mask NP sum (before morph): 24472
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Mask NP sum (after erosion 10x3): 18488
[DEBUG B01_0004.JPG_clsLD_box30_StageA - StageA] Mask NP sum (after dilation 10x3): 24238
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Box Coords Original: [1165. 1076. 1323. 1246.]
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Transformed boxes NP: [795.30666667 734.54933333 903.168      850.60266667]
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9162
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Output mask NP sum (before morph): 15912
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 10233
[DEBUG B01_0004.JPG_clsHD_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 15753
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Box Coords Original: [1033. 1102. 1203. 1260.]
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Transformed boxes NP: [705.19466667 752.29866667 821.248      860.16      ]
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9575
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Output mask NP sum (before morph): 15969
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 10501
[DEBUG B01_0004.JPG_clsHD_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 15841
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Box Coords Original: [397. 178. 607. 370.]
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Transformed boxes NP: [271.01866667 121.51466667 414.37866667 252.58666667]
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9464
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Output mask NP sum (before morph): 22825
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 16092
[DEBUG B01_0004.JPG_clsHD_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 22672
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Box Coords Original: [503. 362. 671. 514.]
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Transformed boxes NP: [343.38133333 247.12533333 458.06933333 350.89066667]
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9446
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Output mask NP sum (before morph): 14888
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 9582
[DEBUG B01_0004.JPG_clsHD_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 14782
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Box Coords Original: [667. 448. 845. 620.]
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Transformed boxes NP: [455.33866667 305.83466667 576.85333333 423.25333333]
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9487
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Output mask NP sum (before morph): 16054
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Mask NP sum (after erosion 10x3): 10622
[DEBUG B01_0004.JPG_clsHD_box4_StageA - StageA] Mask NP sum (after dilation 10x3): 15922
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Box Coords Original: [341. 380. 519. 528.]
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Transformed boxes NP: [232.78933333 259.41333333 354.304      360.448     ]
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9580
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Output mask NP sum (before morph): 14120
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Mask NP sum (after erosion 10x3): 9023
[DEBUG B01_0004.JPG_clsHD_box5_StageA - StageA] Mask NP sum (after dilation 10x3): 13963
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Box Coords Original: [145. 300. 321. 436.]
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Transformed boxes NP: [ 98.98666667 204.8        219.136      297.64266667]
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9511
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Output mask NP sum (before morph): 15788
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Mask NP sum (after erosion 10x3): 10357
[DEBUG B01_0004.JPG_clsHD_box6_StageA - StageA] Mask NP sum (after dilation 10x3): 15717
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Box Coords Original: [211. 440. 365. 552.]
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Transformed boxes NP: [144.04266667 300.37333333 249.17333333 376.832     ]
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9481
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Output mask NP sum (before morph): 13493
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Mask NP sum (after erosion 10x3): 8629
[DEBUG B01_0004.JPG_clsHD_box7_StageA - StageA] Mask NP sum (after dilation 10x3): 13349
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Box Coords Original: [ 65. 464. 241. 630.]
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Transformed boxes NP: [ 44.37333333 316.75733333 164.52266667 430.08      ]
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9521
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Output mask NP sum (before morph): 18002
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Mask NP sum (after erosion 10x3): 12122
[DEBUG B01_0004.JPG_clsHD_box8_StageA - StageA] Mask NP sum (after dilation 10x3): 17862
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Box Coords Original: [199. 554. 407. 728.]
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Transformed boxes NP: [135.85066667 378.19733333 277.84533333 496.98133333]
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9412
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Output mask NP sum (before morph): 16874
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Mask NP sum (after erosion 10x3): 11000
[DEBUG B01_0004.JPG_clsHD_box9_StageA - StageA] Mask NP sum (after dilation 10x3): 16740
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Box Coords Original: [801. 678. 995. 840.]
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Transformed boxes NP: [546.816      462.848      679.25333333 573.44      ]
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9405
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Output mask NP sum (before morph): 15377
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Mask NP sum (after erosion 10x3): 10032
[DEBUG B01_0004.JPG_clsHD_box10_StageA - StageA] Mask NP sum (after dilation 10x3): 15312
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Box Coords Original: [685. 748. 835. 890.]
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Transformed boxes NP: [467.62666667 510.63466667 570.02666667 607.57333333]
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9600
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Output mask NP sum (before morph): 10385
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Mask NP sum (after erosion 10x3): 6063
[DEBUG B01_0004.JPG_clsHD_box11_StageA - StageA] Mask NP sum (after dilation 10x3): 10283
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Box Coords Original: [ 845.  844. 1019. 1042.]
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Transformed boxes NP: [576.85333333 576.17066667 695.63733333 711.33866667]
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9288
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Output mask NP sum (before morph): 11779
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Mask NP sum (after erosion 10x3): 7155
[DEBUG B01_0004.JPG_clsHD_box12_StageA - StageA] Mask NP sum (after dilation 10x3): 11515
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Box Coords Original: [347. 690. 487. 830.]
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Transformed boxes NP: [236.88533333 471.04       332.45866667 566.61333333]
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Selected mask index: 2, Chosen IoU: 0.9436
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Output mask NP sum (before morph): 13804
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Mask NP sum (after erosion 10x3): 8850
[DEBUG B01_0004.JPG_clsHD_box13_StageA - StageA] Mask NP sum (after dilation 10x3): 13670
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Box Coords Original: [205. 736. 363. 852.]
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Transformed boxes NP: [139.94666667 502.44266667 247.808      581.632     ]
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9370
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Output mask NP sum (before morph): 8404
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Mask NP sum (after erosion 10x3): 4574
[DEBUG B01_0004.JPG_clsHD_box14_StageA - StageA] Mask NP sum (after dilation 10x3): 8254
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Box Coords Original: [ 313.  832.  535. 1002.]
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Transformed boxes NP: [213.67466667 567.97866667 365.22666667 684.032     ]
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9057
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Output mask NP sum (before morph): 18024
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Mask NP sum (after erosion 10x3): 12304
[DEBUG B01_0004.JPG_clsHD_box15_StageA - StageA] Mask NP sum (after dilation 10x3): 17884
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Box Coords Original: [ 165.  840.  343. 1048.]
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Transformed boxes NP: [112.64       573.44       234.15466667 715.43466667]
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9498
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Output mask NP sum (before morph): 19306
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Mask NP sum (after erosion 10x3): 13012
[DEBUG B01_0004.JPG_clsHD_box16_StageA - StageA] Mask NP sum (after dilation 10x3): 19152
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Box Coords Original: [ 245. 1040.  385. 1176.]
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Transformed boxes NP: [167.25333333 709.97333333 262.82666667 802.816     ]
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9512
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Output mask NP sum (before morph): 11825
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Mask NP sum (after erosion 10x3): 7111
[DEBUG B01_0004.JPG_clsHD_box17_StageA - StageA] Mask NP sum (after dilation 10x3): 11731
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Box Coords Original: [ 965.  976. 1097. 1110.]
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Transformed boxes NP: [658.77333333 666.28266667 748.88533333 757.76      ]
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9415
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Output mask NP sum (before morph): 10038
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Mask NP sum (after erosion 10x3): 5716
[DEBUG B01_0004.JPG_clsHD_box18_StageA - StageA] Mask NP sum (after dilation 10x3): 9796
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Box Coords Original: [ 811.  984.  981. 1176.]
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Transformed boxes NP: [553.64266667 671.744      669.696      802.816     ]
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9429
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Output mask NP sum (before morph): 18162
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Mask NP sum (after erosion 10x3): 12388
[DEBUG B01_0004.JPG_clsHD_box19_StageA - StageA] Mask NP sum (after dilation 10x3): 18008
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Box Coords Original: [  2. 319. 132. 463.]
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Transformed boxes NP: [  1.36533333 217.77066667  90.112      316.07466667]
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9622
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Output mask NP sum (before morph): 11597
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Mask NP sum (after erosion 10x3): 7918
[DEBUG B01_0004.JPG_clsHD_box20_StageA - StageA] Mask NP sum (after dilation 10x3): 11548
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Box Coords Original: [  1. 647. 140. 807.]
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Transformed boxes NP: [  0.68266667 441.68533333  95.57333333 550.912     ]
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9460
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Output mask NP sum (before morph): 13921
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Mask NP sum (after erosion 10x3): 9693
[DEBUG B01_0004.JPG_clsHD_box21_StageA - StageA] Mask NP sum (after dilation 10x3): 13853
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Box Coords Original: [335. 150. 398. 218.]
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Transformed boxes NP: [228.69333333 102.4        271.70133333 148.82133333]
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9529
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Output mask NP sum (before morph): 3910
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 1625
[DEBUG B01_0004.JPG_clsother_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 3825
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Box Coords Original: [614.   4. 983. 435.]
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Transformed boxes NP: [419.15733333   2.73066667 671.06133333 296.96      ]
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9708
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Output mask NP sum (before morph): 98536
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 82153
[DEBUG B01_0004.JPG_clsother_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 98133
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Box Coords Original: [ 602. 1129.  661. 1196.]
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Transformed boxes NP: [410.96533333 770.73066667 451.24266667 816.46933333]
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9542
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Output mask NP sum (before morph): 2485
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 691
[DEBUG B01_0004.JPG_clsother_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 2271
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Box Coords Original: [1013. 1247. 1178. 1391.]
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Transformed boxes NP: [691.54133333 851.28533333 804.18133333 949.58933333]
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9414
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Output mask NP sum (before morph): 13049
[DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 7560
/datadisk/SAM/generate_initial_pseudo_labels.py:66: RuntimeWarning: overflow encountered in exp
  power_term = np.exp(log_power_clipped)

基于嵌入生成聚合数据集 (train集):   0%|          | 1/667 [00:14<2:38:10, 14.25s/it, image=B01_0004.JPG]
基于嵌入生成聚合数据集 (train集):   0%|          | 1/667 [00:14<2:38:10, 14.25s/it, image=B01_0005.JPG][DEBUG B01_0004.JPG_clsother_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 12600
  🎯 为图像 B01_0004.JPG 生成并聚合FIDT图...
  ✅ FIDT图生成完成，共 57 个框
成功保存图像 B01_0004.JPG 的聚合数据 (57 个框)
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Box Coords Original: [ 609.  972.  769. 1114.]
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Transformed boxes NP: [415.744      663.552      524.97066667 760.49066667]
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9103
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Output mask NP sum (before morph): 10992
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 6275
[DEBUG B01_0005.JPG_clsLD_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 10835
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Box Coords Original: [682.   5. 972. 248.]
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Transformed boxes NP: [465.57866667   3.41333333 663.552      169.30133333]
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9572
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Output mask NP sum (before morph): 44070
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 34769
[DEBUG B01_0005.JPG_clsLD_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 43839
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Box Coords Original: [ 949.    3. 1153.  213.]
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Transformed boxes NP: [647.85066667   2.048      787.11466667 145.408     ]
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9616
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Output mask NP sum (before morph): 28330
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 21995
[DEBUG B01_0005.JPG_clsLD_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 28045
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Box Coords Original: [1318.   88. 1500.  325.]
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Transformed boxes NP: [ 899.75466667   60.07466667 1024.          221.86666667]
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9605
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Output mask NP sum (before morph): 25337
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 19481
[DEBUG B01_0005.JPG_clsLD_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 25181
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Box Coords Original: [1162.  151. 1345.  373.]
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Transformed boxes NP: [793.25866667 103.08266667 918.18666667 254.63466667]
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9417
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Output mask NP sum (before morph): 27320
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Mask NP sum (after erosion 10x3): 19427
[DEBUG B01_0005.JPG_clsLD_box4_StageA - StageA] Mask NP sum (after dilation 10x3): 26947
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Box Coords Original: [1032.  157. 1199.  363.]
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Transformed boxes NP: [704.512      107.17866667 818.51733333 247.808     ]
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9516
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Output mask NP sum (before morph): 22376
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Mask NP sum (after erosion 10x3): 15750
[DEBUG B01_0005.JPG_clsLD_box5_StageA - StageA] Mask NP sum (after dilation 10x3): 22150
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Box Coords Original: [ 903.  194. 1055.  401.]
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Transformed boxes NP: [616.448      132.43733333 720.21333333 273.74933333]
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.8508
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Output mask NP sum (before morph): 17314
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Mask NP sum (after erosion 10x3): 11332
[DEBUG B01_0005.JPG_clsLD_box6_StageA - StageA] Mask NP sum (after dilation 10x3): 16892
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Box Coords Original: [764. 232. 911. 357.]
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Transformed boxes NP: [521.55733333 158.37866667 621.90933333 243.712     ]
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.8940
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Output mask NP sum (before morph): 9859
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Mask NP sum (after erosion 10x3): 5615
[DEBUG B01_0005.JPG_clsLD_box7_StageA - StageA] Mask NP sum (after dilation 10x3): 9715
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Box Coords Original: [716. 353. 951. 515.]
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Transformed boxes NP: [488.78933333 240.98133333 649.216      351.57333333]
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9581
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Output mask NP sum (before morph): 24316
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Mask NP sum (after erosion 10x3): 17396
[DEBUG B01_0005.JPG_clsLD_box8_StageA - StageA] Mask NP sum (after dilation 10x3): 24196
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Box Coords Original: [ 970.  357. 1162.  528.]
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Transformed boxes NP: [662.18666667 243.712      793.25866667 360.448     ]
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9575
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Output mask NP sum (before morph): 20045
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Mask NP sum (after erosion 10x3): 13789
[DEBUG B01_0005.JPG_clsLD_box9_StageA - StageA] Mask NP sum (after dilation 10x3): 19889
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Box Coords Original: [1178.  369. 1337.  499.]
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Transformed boxes NP: [804.18133333 251.904      912.72533333 340.65066667]
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9471
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Output mask NP sum (before morph): 11435
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Mask NP sum (after erosion 10x3): 6910
[DEBUG B01_0005.JPG_clsLD_box10_StageA - StageA] Mask NP sum (after dilation 10x3): 11290
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Box Coords Original: [1316.  426. 1464.  596.]
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Transformed boxes NP: [898.38933333 290.816      999.424      406.86933333]
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9477
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Output mask NP sum (before morph): 13397
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Mask NP sum (after erosion 10x3): 7995
[DEBUG B01_0005.JPG_clsLD_box11_StageA - StageA] Mask NP sum (after dilation 10x3): 13035
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Box Coords Original: [ 970. 1199. 1114. 1361.]
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Transformed boxes NP: [662.18666667 818.51733333 760.49066667 929.10933333]
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9443
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Output mask NP sum (before morph): 11220
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Mask NP sum (after erosion 10x3): 6385
[DEBUG B01_0005.JPG_clsLD_box12_StageA - StageA] Mask NP sum (after dilation 10x3): 11045
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Box Coords Original: [1109. 1138. 1341. 1324.]
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Transformed boxes NP: [757.07733333 776.87466667 915.456      903.85066667]
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9364
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Output mask NP sum (before morph): 25233
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Mask NP sum (after erosion 10x3): 17906
[DEBUG B01_0005.JPG_clsLD_box13_StageA - StageA] Mask NP sum (after dilation 10x3): 24826
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Box Coords Original: [1234. 1292. 1455. 1482.]
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Transformed boxes NP: [ 842.41066667  882.00533333  993.28       1011.712     ]
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9140
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Output mask NP sum (before morph): 23399
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Mask NP sum (after erosion 10x3): 16108
[DEBUG B01_0005.JPG_clsLD_box14_StageA - StageA] Mask NP sum (after dilation 10x3): 22768
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Box Coords Original: [1307. 1107. 1500. 1261.]
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Transformed boxes NP: [ 892.24533333  755.712      1024.          860.84266667]
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9546
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Output mask NP sum (before morph): 15653
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Mask NP sum (after erosion 10x3): 9756
[DEBUG B01_0005.JPG_clsLD_box15_StageA - StageA] Mask NP sum (after dilation 10x3): 15436
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Box Coords Original: [1339.  932. 1500. 1102.]
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Transformed boxes NP: [ 914.09066667  636.24533333 1024.          752.29866667]
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9740
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Output mask NP sum (before morph): 21671
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Mask NP sum (after erosion 10x3): 16292
[DEBUG B01_0005.JPG_clsLD_box16_StageA - StageA] Mask NP sum (after dilation 10x3): 21412
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Box Coords Original: [1353.  769. 1500.  927.]
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Transformed boxes NP: [ 923.648       524.97066667 1024.          632.832     ]
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9620
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Output mask NP sum (before morph): 13298
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Mask NP sum (after erosion 10x3): 8833
[DEBUG B01_0005.JPG_clsLD_box17_StageA - StageA] Mask NP sum (after dilation 10x3): 12813
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Box Coords Original: [ 186. 1234.  355. 1392.]
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Transformed boxes NP: [126.976      842.41066667 242.34666667 950.272     ]
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9411
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Output mask NP sum (before morph): 13359
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Mask NP sum (after erosion 10x3): 7793
[DEBUG B01_0005.JPG_clsLD_box18_StageA - StageA] Mask NP sum (after dilation 10x3): 13033
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Box Coords Original: [ 234. 1357.  390. 1489.]
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Transformed boxes NP: [ 159.744       926.37866667  266.24       1016.49066667]
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9625
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Output mask NP sum (before morph): 16020
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Mask NP sum (after erosion 10x3): 11710
[DEBUG B01_0005.JPG_clsLD_box19_StageA - StageA] Mask NP sum (after dilation 10x3): 15780
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Box Coords Original: [ 383. 1320.  527. 1500.]
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Transformed boxes NP: [ 261.46133333  901.12        359.76533333 1024.        ]
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.8941
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Output mask NP sum (before morph): 13672
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Mask NP sum (after erosion 10x3): 8428
[DEBUG B01_0005.JPG_clsLD_box20_StageA - StageA] Mask NP sum (after dilation 10x3): 13107
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Box Coords Original: [1069. 1297. 1288. 1500.]
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Transformed boxes NP: [ 729.77066667  885.41866667  879.27466667 1024.        ]
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9396
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Output mask NP sum (before morph): 25480
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Mask NP sum (after erosion 10x3): 18935
[DEBUG B01_0005.JPG_clsLD_box21_StageA - StageA] Mask NP sum (after dilation 10x3): 25095
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Box Coords Original: [1367. 1236. 1500. 1438.]
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Transformed boxes NP: [ 933.20533333  843.776      1024.          981.67466667]
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9501
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Output mask NP sum (before morph): 15679
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Mask NP sum (after erosion 10x3): 11324
[DEBUG B01_0005.JPG_clsLD_box22_StageA - StageA] Mask NP sum (after dilation 10x3): 15254
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Box Coords Original: [ 433. 1074.  581. 1182.]
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Transformed boxes NP: [295.59466667 733.184      396.62933333 806.912     ]
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9365
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Output mask NP sum (before morph): 7924
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 4206
[DEBUG B01_0005.JPG_clsHD_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 7846
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Box Coords Original: [ 619. 1120.  801. 1278.]
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Transformed boxes NP: [422.57066667 764.58666667 546.816      872.448     ]
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.8724
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Output mask NP sum (before morph): 15827
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 9894
[DEBUG B01_0005.JPG_clsHD_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 15554
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Box Coords Original: [ 511. 1166.  637. 1330.]
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Transformed boxes NP: [348.84266667 795.98933333 434.85866667 907.94666667]
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9501
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Output mask NP sum (before morph): 15493
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 9885
[DEBUG B01_0005.JPG_clsHD_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 15305
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Box Coords Original: [ 345. 1184.  503. 1356.]
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Transformed boxes NP: [235.52       808.27733333 343.38133333 925.696     ]
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9439
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Output mask NP sum (before morph): 14857
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 9577
[DEBUG B01_0005.JPG_clsHD_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 14697
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Box Coords Original: [   5. 1118.  137. 1248.]
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Transformed boxes NP: [  3.41333333 763.22133333  93.52533333 851.968     ]
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9469
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Output mask NP sum (before morph): 13537
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Mask NP sum (after erosion 10x3): 9384
[DEBUG B01_0005.JPG_clsHD_box4_StageA - StageA] Mask NP sum (after dilation 10x3): 13444
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Box Coords Original: [  61. 1274.  187. 1446.]
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Transformed boxes NP: [ 41.64266667 869.71733333 127.65866667 987.136     ]
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9016
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Output mask NP sum (before morph): 16127
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Mask NP sum (after erosion 10x3): 10561
[DEBUG B01_0005.JPG_clsHD_box5_StageA - StageA] Mask NP sum (after dilation 10x3): 15741
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Box Coords Original: [ 141. 1146.  277. 1244.]
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Transformed boxes NP: [ 96.256      782.336      189.09866667 849.23733333]
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Selected mask index: 2, Chosen IoU: 0.8775
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Output mask NP sum (before morph): 10224
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Mask NP sum (after erosion 10x3): 5559
[DEBUG B01_0005.JPG_clsHD_box6_StageA - StageA] Mask NP sum (after dilation 10x3): 9819
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Box Coords Original: [ 522. 1330.  689. 1471.]
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Transformed boxes NP: [ 356.352       907.94666667  470.35733333 1004.20266667]
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Selected mask index: 2, Chosen IoU: 0.9285
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Output mask NP sum (before morph): 20047
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Mask NP sum (after erosion 10x3): 13845
[DEBUG B01_0005.JPG_clsHD_box7_StageA - StageA] Mask NP sum (after dilation 10x3): 19755
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Box Coords Original: [   2. 1267.   70. 1419.]
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Transformed boxes NP: [  1.36533333 864.93866667  47.78666667 968.704     ]
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9645
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Output mask NP sum (before morph): 7207
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Mask NP sum (after erosion 10x3): 4483
[DEBUG B01_0005.JPG_clsHD_box8_StageA - StageA] Mask NP sum (after dilation 10x3): 7103
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Box Coords Original: [241. 235. 349. 348.]
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Transformed boxes NP: [164.52266667 160.42666667 238.25066667 237.568     ]
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9204
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Output mask NP sum (before morph): 6401
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Mask NP sum (after erosion 10x3): 2971
[DEBUG B01_0005.JPG_clsother_box0_StageA - StageA] Mask NP sum (after dilation 10x3): 6071
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Box Coords Original: [505. 348. 601. 429.]
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Transformed boxes NP: [344.74666667 237.568      410.28266667 292.864     ]
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9738
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Output mask NP sum (before morph): 5779

基于嵌入生成聚合数据集 (train集):   0%|          | 1/667 [00:21<4:00:38, 21.68s/it, image=B01_0005.JPG]
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Mask NP sum (after erosion 10x3): 2556
[DEBUG B01_0005.JPG_clsother_box1_StageA - StageA] Mask NP sum (after dilation 10x3): 5636
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Box Coords Original: [385.  62. 703. 331.]
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Transformed boxes NP: [262.82666667  42.32533333 479.91466667 225.96266667]
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Selected mask index: 0, Chosen IoU: 0.9705
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Output mask NP sum (before morph): 46802
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Mask NP sum (after erosion 10x3): 33499
[DEBUG B01_0005.JPG_clsother_box2_StageA - StageA] Mask NP sum (after dilation 10x3): 45799
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Box Coords Original: [662. 456. 723. 523.]
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Transformed boxes NP: [451.92533333 311.296      493.568      357.03466667]
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9691
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Output mask NP sum (before morph): 2943
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Mask NP sum (after erosion 10x3): 869
[DEBUG B01_0005.JPG_clsother_box3_StageA - StageA] Mask NP sum (after dilation 10x3): 2749
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Box Coords Original: [650. 379. 685. 413.]
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Transformed boxes NP: [443.73333333 258.73066667 467.62666667 281.94133333]
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9479
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Output mask NP sum (before morph): 844
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Mask NP sum (after erosion 10x3): 4
[DEBUG B01_0005.JPG_clsother_box4_StageA - StageA] Mask NP sum (after dilation 10x3): 584
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Box Coords Original: [165. 397. 688. 799.]
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Transformed boxes NP: [112.64       271.01866667 469.67466667 545.45066667]
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9757
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Output mask NP sum (before morph): 113595
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Mask NP sum (after erosion 10x3): 95735
[DEBUG B01_0005.JPG_clsother_box5_StageA - StageA] Mask NP sum (after dilation 10x3): 112495
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Box Coords Original: [369. 376. 419. 418.]
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Transformed boxes NP: [251.904      256.68266667 286.03733333 285.35466667]
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9560
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Output mask NP sum (before morph): 1652
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Mask NP sum (after erosion 10x3): 268
[DEBUG B01_0005.JPG_clsother_box6_StageA - StageA] Mask NP sum (after dilation 10x3): 1508
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Box Coords Original: [207. 144. 309. 239.]
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Transformed boxes NP: [141.312       98.304      210.944      163.15733333]
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.8836
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Output mask NP sum (before morph): 6863
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Mask NP sum (after erosion 10x3): 3480
[DEBUG B01_0005.JPG_clsother_box7_StageA - StageA] Mask NP sum (after dilation 10x3): 6740
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Box Coords Original: [158. 192. 261. 311.]
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Transformed boxes NP: [107.86133333 131.072      178.176      212.30933333]
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Selected mask index: 2, Chosen IoU: 0.8426
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Output mask NP sum (before morph): 12350
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Mask NP sum (after erosion 10x3): 7537
[DEBUG B01_0005.JPG_clsother_box8_StageA - StageA] Mask NP sum (after dilation 10x3): 11977
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Box Coords Original: [  21.  741.  590. 1275.]
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Transformed boxes NP: [ 14.336      505.856      402.77333333 870.4       ]
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9713
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Output mask NP sum (before morph): 194628
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Mask NP sum (after erosion 10x3): 168033
[DEBUG B01_0005.JPG_clsother_box9_StageA - StageA] Mask NP sum (after dilation 10x3): 192083
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Box Coords Original: [ 648. 1200.  990. 1500.]
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Transformed boxes NP: [ 442.368  819.2    675.84  1024.   ]
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9771
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Output mask NP sum (before morph): 79117
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Mask NP sum (after erosion 10x3): 68944
[DEBUG B01_0005.JPG_clsother_box10_StageA - StageA] Mask NP sum (after dilation 10x3): 78774
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Box Coords Original: [ 630.  511. 1360. 1205.]
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Transformed boxes NP: [430.08       348.84266667 928.42666667 822.61333333]
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Prompt Enc Sparse Emb: torch.Size([1, 2, 256]), Dense Emb: torch.Size([1, 256, 64, 64])
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Selected mask index: 1, Chosen IoU: 0.9828
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Output mask NP sum (before morph): 384012
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Mask NP sum (after erosion 10x3): 353193
[DEBUG B01_0005.JPG_clsother_box11_StageA - StageA] Mask NP sum (after dilation 10x3): 382613
  🎯 为图像 B01_0005.JPG 生成并聚合FIDT图...
  ✅ FIDT图生成完成，共 44 个框
Traceback (most recent call last):
  File "/datadisk/SAM/generate_initial_pseudo_labels.py", line 832, in <module>
    main() 
  File "/datadisk/SAM/generate_initial_pseudo_labels.py", line 613, in main
    total_boxes_saved = save_aggregated_data(
  File "/datadisk/SAM/generate_initial_pseudo_labels.py", line 364, in save_aggregated_data
    np.savez_compressed(output_file, **save_data)
  File "/usr/local/miniconda3/envs/sam/lib/python3.10/site-packages/numpy/lib/_npyio_impl.py", line 763, in savez_compressed
    _savez(file, args, kwds, True, allow_pickle=allow_pickle)
  File "/usr/local/miniconda3/envs/sam/lib/python3.10/site-packages/numpy/lib/_npyio_impl.py", line 796, in _savez
    format.write_array(fid, val,
  File "/usr/local/miniconda3/envs/sam/lib/python3.10/site-packages/numpy/lib/format.py", line 759, in write_array
    fp.write(chunk.tobytes('C'))
  File "/usr/local/miniconda3/envs/sam/lib/python3.10/zipfile.py", line 1144, in write
    self._fileobj.write(data)
KeyboardInterrupt
