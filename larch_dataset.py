import os
import json
import torch
import numpy as np
import cv2
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
# import xml.etree.ElementTree as ET # 不再需要XML解析
# from utils import get_augmentation  # 避免MONAI依赖
import torch.nn.functional as F

class LarchDataset(Dataset):
    """落叶松小袋蛾数据集类，用于训练SAM模型"""
    
    def __init__(self, root_dir, split='train', pseudo_mask_override_dir=None):
        """
        初始化数据集
        
        参数:
            root_dir (str): 数据集根目录
            split (str): 'train' 或 'test'
            # transform: 图像增强变换 (已废弃)
            # use_augmented (bool): 是否使用增强数据 (已废弃)
            return_pseudo_mask (bool): 是否返回伪标签 (此参数似乎未被外部使用，且内部逻辑固定为True)
            pseudo_mask_override_dir (str, optional): 用于覆盖默认伪标签目录的路径。
        """
        self.root_dir = root_dir
        self.split = split
        # self.transform = None # 明确不使用增强, get_augmentation() 已处理
        # self.use_augmented = False # 明确不使用增强
        self.return_pseudo_mask = True # 固定返回伪标签
        
        # 加载数据集信息
        with open(os.path.join(root_dir, 'dataset_info.json'), 'r') as f:
            self.dataset_info = json.load(f)
        
        self.classes = self.dataset_info['classes']
        self.class_to_idx = self.dataset_info['class_to_idx']
        self.num_classes = len(self.classes)
        
        # 获取图像列表
        self.image_dir = os.path.join(root_dir, split, 'images')
        # self.fidt_dir = os.path.join(root_dir, split, 'fidt_maps') # FIDT图已废弃
        self.annotation_dir = os.path.join(root_dir, split, 'annotations')
        
        # 伪标签目录逻辑更新
        if pseudo_mask_override_dir is not None:
            self.pseudo_mask_dir = pseudo_mask_override_dir
            print(f"[LarchDataset] 使用覆盖的伪标签目录: {self.pseudo_mask_dir}")
        else:
            self.pseudo_mask_dir = os.path.join(root_dir, f'{split}_pseudo_masks')
            print(f"[LarchDataset] 使用默认伪标签目录: {self.pseudo_mask_dir}")
        
        # 注意：不在此处自动创建伪标签目录，由生成伪标签的脚本负责创建
        # 这样避免无意中创建旧格式的目录
        if not os.path.exists(self.pseudo_mask_dir):
            print(f"[LarchDataset] 伪标签目录 {self.pseudo_mask_dir} 不存在。请先运行伪标签生成脚本。")
        
        self.image_files = sorted([f for f in os.listdir(self.image_dir) 
                                 if f.endswith(('.jpg', '.JPG')) and '_aug' not in f and '_mixed' not in f])
        
        # 如果没有指定增强转换，使用默认 (albumentations)
        # if self.transform is None and self.use_augmented: # 增强已废弃
            # 这里的 transform 主要是指用于数据增强的 albumentations transform
            # SAM 自身的 ResizeLongestSide 是独立于此的，在训练脚本中应用
            # self.transform = get_augmentation() 
        # elif not self.use_augmented:
            # self.transform = None # 明确不使用增强
    
    def __len__(self):
        return len(self.image_files)
    
    # 重命名并修改为解析JSON
    def parse_json_annotation(self, json_path):
        """解析JSON标注文件"""
        try:
            with open(json_path, 'r') as f:
                annotation_data = json.load(f)
        except Exception as e:
            print(f"错误: 无法解析JSON文件 {json_path}: {e}")
            return {cls: [] for cls in self.classes} # 返回空字典
            
        annotations_by_class = {cls: [] for cls in self.classes}
        
        # JSON 格式是 {'width': ..., 'height': ..., 'objects': [{'bbox': [...], 'class_id': ..., ...}]} 
        for obj in annotation_data.get('objects', []):
            class_id = obj.get('class_id')
            bbox = obj.get('bbox')
            
            if class_id is not None and bbox is not None and 0 <= class_id < self.num_classes:
                cls_name = self.classes[class_id]
                # 确保bbox是包含4个数字的列表或元组
                if isinstance(bbox, (list, tuple)) and len(bbox) == 4:
                    # 尝试将坐标转换为浮点数，以防JSON中是字符串
                    try:
                        bbox_float = [float(coord) for coord in bbox]
                        annotations_by_class[cls_name].append({
                            'class': cls_name,
                            'bbox': bbox_float
                        })
                    except ValueError:
                        print(f"警告: JSON文件 {json_path} 中bbox坐标无法转换为数字: {bbox}。跳过此对象。")
                else:
                    print(f"警告: JSON文件 {json_path} 中bbox格式无效: {bbox}。跳过此对象。")
            else:
                print(f"警告: JSON文件 {json_path} 中对象缺少class_id或bbox，或class_id无效: {obj}。跳过此对象。")

        return annotations_by_class

    def __getitem__(self, idx):
        """获取数据集中的一个样本"""
        image_file = self.image_files[idx]
        base_name = os.path.splitext(image_file)[0]
        
        json_annotation_path = os.path.join(self.annotation_dir, base_name + '.json')
        annotations_by_class_parsed = self.parse_json_annotation(json_annotation_path) 
            # annotations_by_class_parsed is like {'H': [{'class': 'H', 'bbox': [x,y,x,y]}, ...], ...}

        image_path = os.path.join(self.image_dir, image_file)
        try:
            image_pil = Image.open(image_path).convert('RGB')
            image_np_original = np.array(image_pil) # HWC, uint8
        except Exception as e:
            print(f"错误: 加载图像失败 {image_path}: {e}. 返回包含错误信息的样本。")
            return {
                'error': f"无法加载图像 {image_path}",
                'image_name': image_file,
                'image_path': image_path # 即使加载失败也返回路径
            }

        # --- 提取原始边界框 (用于伪标签更新时作为提示) ---
        # 这个 raw_boxes_by_class 将包含相对于原始图像尺寸的坐标
        # 格式: {'class_name': torch.tensor([[x1,y1,x2,y2], ...]), ...}
        raw_boxes_by_class: dict = {cls_name: [] for cls_name in self.classes}
        for cls_name, anns in annotations_by_class_parsed.items():
            for ann in anns:
                raw_boxes_by_class[cls_name].append(ann['bbox'])
        for cls_name in raw_boxes_by_class:
            if raw_boxes_by_class[cls_name]:
                raw_boxes_by_class[cls_name] = torch.tensor(raw_boxes_by_class[cls_name], dtype=torch.float32)
            else:
                raw_boxes_by_class[cls_name] = torch.empty((0, 4), dtype=torch.float32)
        # --- 原始边界框提取结束 ---

        # FIDT 图加载 (保持不变, 基于原始图像尺寸)
        # fidt_maps_np_original = [] # FIDT图已废弃
        # for cls_name in self.classes:
        #     fidt_file = f"{base_name}_{cls_name}.npy"
        #     fidt_path = os.path.join(self.fidt_dir, fidt_file)
        #     if os.path.exists(fidt_path):
        #         try:
        #             fidt_map = np.load(fidt_path).astype(np.float32)
        #             if fidt_map.shape != image_np_original.shape[:2]:
        #                 fidt_map = cv2.resize(fidt_map, (image_np_original.shape[1], image_np_original.shape[0]), interpolation=cv2.INTER_LINEAR)
        #         except Exception as e:
        #             print(f"错误：加载或调整FIDT图失败 {fidt_path}: {e}。将使用零图。")
        #             fidt_map = np.zeros(image_np_original.shape[:2], dtype=np.float32)
        #     else:
        #         fidt_map = np.zeros(image_np_original.shape[:2], dtype=np.float32)
        #     fidt_maps_np_original.append(fidt_map)
        # fidt_maps_stacked_original = np.stack(fidt_maps_np_original, axis=0) # [C, H_orig, W_orig]

        # 伪标签加载 (路径已根据 pseudo_mask_override_dir 更新)
        # pseudo_mask_dir 在 __init__ 中已正确设置
        # 先尝试加载更新后的伪标签 (格式: {base_name}.npy)
        pseudo_mask_path = os.path.join(self.pseudo_mask_dir, f"{base_name}.npy")
        # 如果不存在，尝试加载初始伪标签 (格式: {base_name}_pseudo_mask.npy)
        if not os.path.exists(pseudo_mask_path):
            pseudo_mask_path = os.path.join(self.pseudo_mask_dir, f"{base_name}_pseudo_mask.npy")
        
        # print(f"[LarchDataset GetItem] 尝试加载伪标签: {pseudo_mask_path}") # 调试日志
        pseudo_mask_np_loaded = np.zeros(image_np_original.shape[:2], dtype=np.uint8) # 默认零掩码
        if self.return_pseudo_mask:
            if os.path.exists(pseudo_mask_path):
                try:
                    loaded_mask_content = np.load(pseudo_mask_path)
                    if loaded_mask_content.ndim == 3 and loaded_mask_content.shape[0] == self.num_classes:
                        # [C, H, W] 二进制掩码格式 -> [H, W] 类别索引格式
                        # 每个像素找到第一个激活的类别（值为1的类别）
                        pseudo_mask_np_loaded = np.zeros(loaded_mask_content.shape[1:], dtype=np.uint8)
                        for class_idx in range(self.num_classes):
                            # 对于每个类别，将其掩码中为1的像素设置为该类别的索引
                            class_mask = loaded_mask_content[class_idx] > 0.5
                            # 只更新还没有分配类别的像素（避免重叠）
                            unassigned_pixels = pseudo_mask_np_loaded == 0
                            pseudo_mask_np_loaded[class_mask & unassigned_pixels] = class_idx
                    elif loaded_mask_content.ndim == 2:
                        # Already [H, W] index map
                        pseudo_mask_np_loaded = loaded_mask_content.astype(np.uint8)
                    else:
                        print(f"警告：加载的伪标签形状异常: {pseudo_mask_path} (形状: {loaded_mask_content.shape})。将使用零掩码。")
                except Exception as e:
                    print(f"错误：加载伪标签失败 {pseudo_mask_path}: {e}. 将使用零掩码。")
            # else: # 如果文件不存在，则保持为零掩码，不再打印"文件不存在"信息，除非是调试
                # print(f"信息: 伪标签文件 {pseudo_mask_path} 不存在。将使用零掩码。")
            
        # --- 数据增强 (如果启用) ---
        # 数据增强应用于: image_np_original, fidt_maps_stacked_original (逐通道), pseudo_mask_np_loaded (索引图)
        # 边界框也需要相应转换 (annotations_by_class_parsed)
        
        # 准备用于增强的边界框列表: [[x,y,x,y, class_id], ...]
        bboxes_for_aug = []
        for cls_name, anns in annotations_by_class_parsed.items():
            for ann in anns:
                bboxes_for_aug.append(ann['bbox'] + [self.class_to_idx[cls_name]])

        image_np_aug = image_np_original.copy()
        # fidt_maps_np_aug = fidt_maps_stacked_original.copy() # [C,H,W] # FIDT图已废弃
        pseudo_mask_np_aug = pseudo_mask_np_loaded.copy() # [H,W]
        bboxes_aug = bboxes_for_aug.copy()

        # if self.transform and self.use_augmented and self.split == 'train': # 增强已废弃，此块逻辑不再执行
            # Albumentations 要求 FIDT 和伪掩码作为 'masks' 列表传入
            # FIDT 是多通道的，需要分别处理或确保增强库支持
            # 这里简单地对每个FIDT通道应用相同的空间变换
            # 伪掩码是单通道索引图
            # masks_for_aug = [pseudo_mask_np_aug] # 只有伪标签
            
            # augmented_data = self.transform(
            #     image=image_np_original, 
            #     masks=masks_for_aug, # 只包含伪标签
            #     bboxes=bboxes_for_aug, # 格式 [[x,y,x,y, class_id], ...]
            #     category_id=[box[-1] for box in bboxes_for_aug] # 仅用于 bbox_params，与上面 class_id 一致
            # )
            # image_np_aug = augmented_data['image']
            # augmented_masks_returned = augmented_data['masks']
            # bboxes_aug = augmented_data['bboxes'] # 返回的是 [[x,y,x,y, class_id], ...]
            
            # if augmented_masks_returned: # 确保列表不为空
            #     pseudo_mask_np_aug = augmented_masks_returned[0] # 现在只有一个掩码（伪标签）
            # else:
            #     # 如果增强后masks列表为空（不太可能，但作为防御性编程），则保持原始伪标签
            #     pseudo_mask_np_aug = pseudo_mask_np_loaded.copy()
        
        # --- 将增强（或原始）数据转换为Tensor ---
        # 图像: HWC, uint8 -> CHW, float, [0,1]
        image_tensor = torch.from_numpy(image_np_aug.transpose(2, 0, 1)).float() / 255.0
        
        # FIDT图: [C, H, W], float
        # fidt_tensor = torch.from_numpy(fidt_maps_np_aug).float() # FIDT图已废弃
        
        # 伪标签: [H, W], uint8 -> [H, W], long
        pseudo_mask_tensor = torch.from_numpy(pseudo_mask_np_aug).long()

        # --- 准备用于训练的边界框和点提示 (基于增强后的数据) ---
        # boxes_by_class: {'class_name': torch.tensor([[x1,y1,x2,y2], ...]), ...}
        # points_by_class: {'class_name': torch.tensor([[x,y], ...]), ...}
        # point_labels_by_class: {'class_name': torch.tensor([1, ...]), ...}
        
        boxes_by_class_final: dict = {cls_name: [] for cls_name in self.classes}
        points_by_class_final: dict = {cls_name: [] for cls_name in self.classes}
        point_labels_by_class_final: dict = {cls_name: [] for cls_name in self.classes}

        for bbox_data_aug in bboxes_aug:
            x1, y1, x2, y2, class_id_aug = bbox_data_aug
            cls_name_aug = self.classes[int(class_id_aug)]
            boxes_by_class_final[cls_name_aug].append([x1, y1, x2, y2])
            
            # 生成点提示 (例如，bbox中心点)
            # 对于SAM，通常一个正点就足够，如果提供box的话
            # 这里简化，如果需要更复杂的点提示策略，可以扩展
            # cx, cy = (x1 + x2) / 2, (y1 + y2) / 2
            # points_by_class_final[cls_name_aug].append([cx, cy])
            # point_labels_by_class_final[cls_name_aug].append(1) # 正点

        for cls_name in self.classes:
            if boxes_by_class_final[cls_name]:
                boxes_by_class_final[cls_name] = torch.tensor(boxes_by_class_final[cls_name], dtype=torch.float32)
            else:
                boxes_by_class_final[cls_name] = torch.empty((0, 4), dtype=torch.float32)
            
            if points_by_class_final[cls_name]:
                points_by_class_final[cls_name] = torch.tensor(points_by_class_final[cls_name], dtype=torch.float32)
                point_labels_by_class_final[cls_name] = torch.tensor(point_labels_by_class_final[cls_name], dtype=torch.int64)
            else:
                points_by_class_final[cls_name] = torch.empty((0, 2), dtype=torch.float32)
                point_labels_by_class_final[cls_name] = torch.empty((0,), dtype=torch.int64)

        sample = {
            'image_path': image_path, # 原始图像路径
            'image_name': image_file,
            'image': image_tensor, # 增强或原始图像张量 (CHW, float, [0,1])
            # 'fidt_maps_by_class': fidt_tensor, # Dict mapping or Tensor [C,H,W] - currently Tensor # FIDT图已废弃
            'pseudo_mask': pseudo_mask_tensor, # 增强或原始伪标签张量 (HW, long)
            'boxes_by_class': boxes_by_class_final, # 用于SAM训练提示 (增强后)
            'raw_boxes_by_class': raw_boxes_by_class, # <<< 原始边界框 (用于伪标签更新)
            'points_by_class': points_by_class_final, # (增强后)
            'point_labels_by_class': point_labels_by_class_final, # (增强后)
            'original_image_size': image_np_original.shape[:2] # (H, W)
        }
        return sample

def collate_fn(batch):
    """自定义 collate 函数处理列表形式的批次和可能的 None 值"""
    # 过滤掉加载或增强失败的样本 (返回 None 或包含 'error' 键的字典)
    batch = [item for item in batch if item is not None and 'error' not in item]
    
    # 如果过滤后批次为空，返回 None 或引发错误
    if not batch:
        return None 
        
    # --- 如果 batch_size=1，直接返回第一个有效样本 --- 
    if len(batch) == 1:
        # 但仍然需要确保返回的是字典，而不是列表
        return batch[0]
        
    # --- 如果 batch_size > 1 (理论上不推荐，但尝试处理) --- 
    # 注意：这里的实现假设所有样本增强后尺寸一致，且目标是返回包含列表的字典
    # 这可能与训练脚本中处理批次的逻辑不完全匹配，训练脚本目前假设batch_size=1
    print(f"警告: Collate function 正在处理 batch_size={len(batch)}。训练脚本可能未适配。")
    
    collated_batch = {}
    # 获取第一个有效样本的所有键
    keys = batch[0].keys()
    
    for key in keys:
        # 将所有样本的该键值收集到一个列表中
        collated_batch[key] = [item[key] for item in batch]
        
    # 尝试堆叠可以堆叠的张量 (例如 image, pseudo_mask, fidt_maps)
    # 注意：这假设所有图像和掩码在增强后具有相同的大小！
    try:
        if all(isinstance(item['image'], torch.Tensor) for item in batch):
            collated_batch['image'] = torch.stack(collated_batch['image'])
        if all(isinstance(item['pseudo_mask'], torch.Tensor) for item in batch):
            collated_batch['pseudo_mask'] = torch.stack(collated_batch['pseudo_mask'])
        # fidt_maps_by_class 是字典，无法直接堆叠，保持列表
        # boxes_by_class, points_by_class, point_labels_by_class 也是字典，保持列表
    except Exception as e:
        print(f"警告: Collate function 堆叠张量时出错 (可能是尺寸不匹配): {e}")
        # 如果堆叠失败，保持为列表

    return collated_batch


def get_larch_dataloader(root_dir, split='train', batch_size=1, shuffle=True, num_workers=0, return_dataset=False, pseudo_mask_override_dir=None):
    """
    获取Larch数据集的数据加载器
    
    参数:
        root_dir (str): 数据集根目录
        split (str): 'train' 或 'test'
        batch_size (int): 批次大小
        shuffle (bool): 是否打乱数据
        num_workers (int): 数据加载的工作线程数
        # use_augmented (bool): 是否使用数据增强 (已废弃)
        return_dataset (bool): 是否返回Dataset实例而不是DataLoader
        pseudo_mask_override_dir (str, optional): 覆盖伪标签目录的路径。
    """
    # print(f"[get_larch_dataloader] For split '{split}', use_augmented={use_augmented}")
    
    # transform_to_apply = get_augmentation() if (use_augmented and split == 'train') else None
    # 根据 LarchDataset 的 __init__ 逻辑，transform 主要控制增强，SAM的预处理不在此处
    # 如果 use_augmented=False, LarchDataset内部会将 self.transform 设置为 None
    # 如果 use_augmented=True, LarchDataset内部会使用 get_augmentation() (如果 self.transform 是 None)
    # 因此，这里传递的 transform 可以为 None，由 LarchDataset 内部决定是否应用增强 transform
    # current_transform = None # 数据增强已废弃
    # if use_augmented and split == 'train':
        # current_transform = get_augmentation() # 应用于训练集的数据增强
    
    dataset = LarchDataset(
        root_dir=root_dir,
        split=split,
        # transform=None, # 数据增强已废弃
        # use_augmented=False, # 数据增强已废弃
        # return_pseudo_mask=True, # 内部固定为True
        pseudo_mask_override_dir=pseudo_mask_override_dir # <<< 传递覆盖路径
    )
    
    if return_dataset:
        return dataset
        
    # print(f"[get_larch_dataloader] Dataset for {split} initialized. Size: {len(dataset)}")
        
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle if split == 'train' else False, # 测试集不打乱
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=collate_fn if batch_size > 1 else None # 仅当batch_size > 1时使用自定义collate_fn
    )
    return dataloader

# --- 旧的 prepare_sam_input 函数，保留用于参考或可能的推理 --- #
# 用于SAM的提示编码器输入准备函数
def prepare_sam_input(image, boxes, sam_image_size=1024): # fidt_maps 参数已移除
    """
    准备SAM模型的输入
    
    参数:
        image (torch.Tensor): 图像张量 [C, H, W]
        boxes (torch.Tensor): 框张量 [N, 4]
        # fidt_maps (torch.Tensor): FIDT图张量 [C, H, W] # 已移除
        sam_image_size (int): SAM模型的图像大小
    
    返回:
        dict: SAM模型的输入
    """
    # 调整图像大小
    # transform = ResizeLongestSide(sam_image_size)  # 已注释，避免导入错误
    from segment_anything.utils.transforms import ResizeLongestSide
    transform = ResizeLongestSide(sam_image_size)
    image_size = image.shape[-2:]
    image_np = image.permute(1, 2, 0).cpu().numpy()
    image_resized = transform.apply_image(image_np)
    image_tensor = torch.as_tensor(image_resized).permute(2, 0, 1).contiguous()
    
    # 调整框大小
    boxes_tensor = torch.empty((0, 4), dtype=torch.float32)
    if boxes.shape[0] > 0:
        boxes_np = boxes.cpu().numpy()
        boxes_resized = transform.apply_boxes(boxes_np, image_size)
        boxes_tensor = torch.as_tensor(boxes_resized, dtype=torch.float32)
    
    # 调整FIDT图大小
    # fidt_maps_tensor = torch.empty((0, sam_image_size, sam_image_size), dtype=torch.float32) # 已移除
    # if fidt_maps.shape[0] > 0:
    #     fidt_maps_resized = F.interpolate(
    #         fidt_maps.unsqueeze(0), 
    #         size=(sam_image_size, sam_image_size), 
    #         mode='bilinear', 
    #         align_corners=False
    #     ).squeeze(0)
    #     fidt_maps_tensor = fidt_maps_resized
    
    return {
        'image': image_tensor,
        'boxes': boxes_tensor,
        # 'fidt_maps': fidt_maps_tensor, # 已移除
        'original_size': image_size
    } 

class PerBoxLarchDataset(Dataset):
    """
    修改后的数据集类，每次返回一个框对应的数据
    """
    def __init__(self, data_root, split='train'):
        self.data_root = data_root
        self.split = split
        self.box_samples = []  # [(metadata_path, box_info), ...]
        self.metadata_files = []  # 保存所有元数据文件路径，用于快速权重计算
        
        # 扫描所有元数据文件，构建框级别的样本列表
        metadata_dir = os.path.join(data_root, f"{split}_pseudo_masks_per_box", "metadata")
        
        if not os.path.exists(metadata_dir):
            raise FileNotFoundError(f"元数据目录不存在: {metadata_dir}")
        
        for metadata_file in os.listdir(metadata_dir):
            if metadata_file.endswith('_boxes_info.json'):
                metadata_path = os.path.join(metadata_dir, metadata_file)
                self.metadata_files.append(metadata_path)  # 保存路径用于权重计算
                
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    
                    for box_info in metadata['boxes_info']:
                        self.box_samples.append((metadata_path, box_info))
                except Exception as e:
                    print(f"警告: 跳过无效的元数据文件 {metadata_file}: {e}")
        
        print(f"找到 {len(self.box_samples)} 个框样本用于 {split} 集")
    
    def __len__(self):
        return len(self.box_samples)
    
    def __getitem__(self, idx):
        metadata_path, box_info = self.box_samples[idx]
        
        # 从元数据获取图像名称
        with open(metadata_path, 'r') as f:
            metadata = json.load(f)
        image_name = metadata['image_name']
        
        # 加载图像
        image_path = os.path.join(self.data_root, self.split, 'images', image_name)
        image = Image.open(image_path).convert('RGB')
        
        # 转换为tensor
        image_transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        image_tensor = image_transform(image)
        
        # 加载单个框的掩码
        mask_file = box_info['mask_file']
        mask_path = os.path.join(self.data_root, f"{self.split}_pseudo_masks_per_box", "masks", mask_file)
        
        # 从压缩文件加载完整掩码
        mask_data = np.load(mask_path)
        full_mask = mask_data['mask']
        
        return {
            'image': image_tensor,
            'mask': torch.from_numpy(full_mask).float(),  # 保持2D格式 [H, W]
            'bbox': torch.tensor(box_info['bbox'], dtype=torch.float32),
            'class_id': box_info['class_id'],
            'class_name': box_info['class_name'],
            'image_name': image_name
        }

class AggregatedPerBoxDataset(Dataset):
    """
    基于聚合.npz文件的按框数据集类
    每个样本对应一个框的数据，包含预计算的图像嵌入
    """
    def __init__(self, data_root, split='train'):
        self.data_root = data_root
        self.split = split
        self.box_map = []  # [(file_path, internal_box_index, class_id, class_name), ...]
        
        # 扫描聚合数据集目录
        aggregated_dir = os.path.join(data_root, f"aggregated_dataset/{split}")
        if not os.path.exists(aggregated_dir):
            raise FileNotFoundError(f"聚合数据集目录不存在: {aggregated_dir}")
        
        print(f"扫描聚合数据集目录: {aggregated_dir}")
        
        # 预先扫描所有.npz文件，构建框到文件的映射
        for filename in os.listdir(aggregated_dir):
            if filename.endswith('.npz'):
                file_path = os.path.join(aggregated_dir, filename)
                
                try:
                    # 快速加载文件头部信息
                    with np.load(file_path, mmap_mode='r') as data:
                        total_boxes = int(data['total_boxes'])
                        if total_boxes > 0:
                            class_ids = data['class_ids']  # [N]
                            class_names = data['class_names']  # [N] list
                            
                            for box_idx in range(total_boxes):
                                self.box_map.append((
                                    file_path, 
                                    box_idx, 
                                    int(class_ids[box_idx]), 
                                    str(class_names[box_idx])
                                ))
                                
                except Exception as e:
                    print(f"警告: 跳过无效的聚合文件 {filename}: {e}")
        
        print(f"找到 {len(self.box_map)} 个框样本用于 {split} 集")
        
        # LRU缓存用于避免重复加载同一文件
        self._cache = {}
        self._cache_order = []
        self._max_cache_size = 10
    
    def __len__(self):
        return len(self.box_map)
    
    def _load_file_with_cache(self, file_path):
        """使用简单的LRU缓存加载文件"""
        if file_path in self._cache:
            # 移动到最近使用
            self._cache_order.remove(file_path)
            self._cache_order.append(file_path)
            return self._cache[file_path]
        
        # 加载新文件
        data = np.load(file_path)
        
        # 添加到缓存
        self._cache[file_path] = data
        self._cache_order.append(file_path)
        
        # 维护缓存大小
        while len(self._cache) > self._max_cache_size:
            oldest_file = self._cache_order.pop(0)
            if oldest_file in self._cache:
                self._cache[oldest_file].close()  # 关闭numpy数据
                del self._cache[oldest_file]
        
        return data
    
    def __getitem__(self, idx):
        file_path, internal_box_index, class_id, class_name = self.box_map[idx]
        
        try:
            # 使用缓存加载聚合文件
            data = self._load_file_with_cache(file_path)
            
            # 提取数据
            image_name = str(data['image_name'])
            image_size = tuple(data['image_size'])
            image_embedding = torch.from_numpy(data['image_embedding']).float()
            
            # 提取单个框的数据
            bbox = torch.from_numpy(data['boxes'][internal_box_index]).float()  # [4]
            mask = torch.from_numpy(data['masks'][internal_box_index]).float()  # [H, W]

            # 提取FIDT图（新增）
            fidt_map = None
            if 'fidt_maps' in data and data['fidt_maps'].shape[0] > internal_box_index:
                fidt_map = torch.from_numpy(data['fidt_maps'][internal_box_index]).float()  # [H, W]
            else:
                # 如果没有FIDT图，创建零图作为占位符
                fidt_map = torch.zeros_like(mask)

            return {
                'embedding': image_embedding,  # 预计算的嵌入 [1, 256, 64, 64]
                'mask': mask,  # 目标掩码 [H, W]
                'fidt_map': fidt_map,  # FIDT图 [H, W] 新增
                'bbox': bbox,  # 边界框 [4]
                'class_id': class_id,
                'class_name': class_name,
                'box_id': idx,  # 使用数据集索引作为唯一的box_id
                'image_name': image_name,
                'image_size': image_size
            }
            
        except Exception as e:
            print(f"错误: 加载聚合数据失败 (idx={idx}, file={file_path}): {e}")
            # 返回空样本
            return {
                'embedding': torch.zeros(1, 256, 64, 64),
                'mask': torch.zeros(100, 100),  # 默认尺寸
                'fidt_map': torch.zeros(100, 100),  # 默认FIDT图
                'bbox': torch.zeros(4),
                'class_id': 0,
                'class_name': 'unknown',
                'image_name': 'error',
                'image_size': (100, 100)
    }