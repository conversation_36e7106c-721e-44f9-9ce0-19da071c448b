import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm
import random
import json
import argparse
import traceback
import warnings
import time
import math
from typing import List, Optional

# 抑制MONAI距离计算相关的警告
warnings.filterwarnings("ignore", message=".*the ground truth.*is all 0.*may result in nan/inf distance.*")
warnings.filterwarnings("ignore", message=".*the prediction.*is all 0.*may result in nan/inf distance.*")
warnings.filterwarnings("ignore", category=UserWarning, module="monai.metrics.utils")

# 抑制PyTorch AMP相关的FutureWarning
warnings.filterwarnings("ignore", message=".*torch.cuda.amp.autocast.*is deprecated.*")
warnings.filterwarnings("ignore", message=".*torch.cuda.amp.GradScaler.*is deprecated.*")
warnings.filterwarnings("ignore", category=FutureWarning, module="torch.cuda.amp")
warnings.filterwarnings("ignore", category=FutureWarning)

from segment_anything import sam_model_registry
from segment_anything.utils.transforms import ResizeLongestSide

try:
    import monai
    from monai.metrics.meandice import DiceMetric
    from monai.metrics.hausdorff_distance import HausdorffDistanceMetric
    from monai.metrics.confusion_matrix import ConfusionMatrixMetric
    from monai.data.utils import decollate_batch # For handling batches of predictions/targets for metrics
    from monai.transforms.post.array import AsDiscrete # For converting predictions to discrete values
    MONAI_AVAILABLE = True
except ImportError:
    print("Warning: MONAI not available, some advanced metrics may not work")
    MONAI_AVAILABLE = False

import torch.nn.functional as F
from PIL import Image
import cv2
from larch_dataset import get_larch_dataloader
from utils import set_seed

try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    print("Warning: TensorBoard not available, will skip logging")
    TENSORBOARD_AVAILABLE = False
    # 创建一个假的SummaryWriter类避免导入错误
    class DummySummaryWriter:
        def __init__(self, *args, **kwargs):
            pass
        def add_scalar(self, *args, **kwargs):
            pass
        def close(self):
            pass
    SummaryWriter = DummySummaryWriter

import logging
import matplotlib.pyplot as plt
from wt_enhanced_block import WTEnhancedBlock
from datetime import datetime

# ==================== 基于不确定性的自动加权损失 ====================
class AutomaticWeightedLoss(nn.Module):
    """
    基于不确定性的自动加权损失
    
    实现基于文章 "Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics"
    的不确定性加权方法。
    
    核心思想：
    - 总损失 = Σ(1/(2*σ_i^2) * L_i + log(σ_i))
    - σ_i 是可学习参数，代表第i个任务的不确定性
    - 不确定性高的任务权重自动降低
    """
    def __init__(self, num_losses=2):
        super().__init__()
        # 初始化可学习的log(σ^2)参数
        # 使用log(σ^2)而不是σ^2以确保数值稳定性
        self.log_vars = nn.Parameter(torch.zeros(num_losses))
        self.num_losses = num_losses
    
    def forward(self, *losses):
        """
        Args:
            *losses: 各个损失值 (loss1, loss2, ...)
        
        Returns:
            total_loss: 加权后的总损失
        """
        if len(losses) != self.num_losses:
            raise ValueError(f"期望 {self.num_losses} 个损失，但得到 {len(losses)} 个")
        
        total_loss = 0
        for i, loss in enumerate(losses):
            # 计算精度：1/σ^2 = exp(-log(σ^2))
            precision = torch.exp(-self.log_vars[i])
            
            # 加权损失：1/(2*σ^2) * L_i + log(σ) = 0.5 * precision * L_i + 0.5 * log_vars[i]
            weighted_loss = 0.5 * precision * loss + 0.5 * self.log_vars[i]
            total_loss += weighted_loss
        
        return total_loss
    
    def get_uncertainty_weights(self):
        """获取当前的不确定性权重σ"""
        return torch.exp(0.5 * self.log_vars)
    
    def get_loss_weights(self):
        """获取当前的损失权重 1/(2*σ^2)"""
        return 0.5 * torch.exp(-self.log_vars)
    
    def get_loss_weights_info(self):
        """获取当前的损失权重信息（用于日志记录）"""
        uncertainty_weights = self.get_uncertainty_weights()
        loss_weights = self.get_loss_weights()
        
        return {
            'dice_focal_uncertainty': uncertainty_weights[0].item(),
            'auc_uncertainty': uncertainty_weights[1].item(),
            'dice_focal_weight': loss_weights[0].item(),
            'auc_weight': loss_weights[1].item()
        }

# ==================== 多标签AUC损失实现 ====================
def _get_auc_loss_function(loss_type: str):
    """AUC损失函数"""
    if loss_type == 'log_loss':
        return lambda x, epsilon: torch.log(1 + torch.exp(-x + epsilon))
    elif loss_type == 'hinge_loss':
        return lambda x, epsilon: torch.clamp(1 - x + epsilon, min=0)
    elif loss_type == 'exp_loss':
        return lambda x, epsilon: torch.exp(-x + epsilon)
    else:
        raise ValueError(f"不支持的损失类型: {loss_type}")


# ==================== 原有代码继续 ====================

# 配置日志记录 - 修改为DEBUG级别以显示所有调试信息
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 简化的日志处理器
class SimpleLogger:
    def __init__(self, log_file_path):
        self.log_file_path = log_file_path
        self.logger = None
        self.is_active = log_file_path is not None
        if self.is_active:
            self.setup_logging()
        
    def setup_logging(self):
        """设置简单的文件日志"""
        with open(self.log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"=== FIDT-SAM 训练日志 ===\n")
            f.write(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 50 + "\n\n")
    
    def log(self, message):
        """记录日志信息 - 同时输出到文件和终端"""
        if not self.is_active:
            return
        log_line = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"
        # 输出到文件
        with open(self.log_file_path, 'a', encoding='utf-8') as f:
            f.write(log_line + "\n")
        # 输出到终端
        print(log_line)
    
    def cleanup(self):
        """清理日志"""
        if not self.is_active:
            return
        with open(self.log_file_path, 'a', encoding='utf-8') as f:
            f.write(f"\n训练结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

# 🚀 新增：bbox_mask 创建函数，用于限制损失和指标计算范围
def create_bbox_mask(mask_shape, bbox, device=None):
    """
    创建bbox范围内的掩码，用于限制损失和指标计算范围
    
    Args:
        mask_shape: 掩码形状，如 (H, W) 或 (1, H, W) 或 (B, H, W)
        bbox: 边界框坐标 [x1, y1, x2, y2] 或 torch.Tensor
        device: 设备
    
    Returns:
        bbox_mask: 与输入形状相同的二值掩码，框内为1，框外为0
    """
    if len(mask_shape) >= 2:
        H, W = mask_shape[-2:]
    else:
        raise ValueError(f"mask_shape 至少需要2维: {mask_shape}")
    
    if device is None:
        device = torch.device('cpu')
    bbox_mask = torch.zeros(H, W, device=device)
    
    # 处理不同类型的bbox输入
    if isinstance(bbox, torch.Tensor):
        bbox = bbox.cpu().numpy()
    
    x1, y1, x2, y2 = bbox
    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
    
    # 确保坐标在有效范围内
    x1 = max(0, min(x1, W-1))
    y1 = max(0, min(y1, H-1))
    x2 = max(x1, min(x2, W-1))
    y2 = max(y1, min(y2, H-1))
    
    bbox_mask[y1:y2+1, x1:x2+1] = 1.0
    
    # 根据输入形状调整输出形状
    if len(mask_shape) == 3 and mask_shape[0] == 1:
        bbox_mask = bbox_mask.unsqueeze(0)  # 添加channel维度 [1, H, W]
    elif len(mask_shape) == 4:
        bbox_mask = bbox_mask.unsqueeze(0).unsqueeze(0)  # 添加batch和channel维度 [1, 1, H, W]
    
    return bbox_mask


def bbox_to_center_point(bbox, sam_transform=None, original_size=None, device=None):
    """
    将边界框转换为中心点坐标，用于点提示模式

    Args:
        bbox: 边界框张量，形状为 [4] 或 [1, 4] 或 [B, 4]，格式为 (x1, y1, x2, y2)
        sam_transform: SAM变换对象，用于坐标变换
        original_size: 原始图像尺寸 (height, width)，用于坐标变换
        device: 目标设备

    Returns:
        point_coords: 形状为 [B, 1, 2] 的张量，表示中心点坐标 (x, y)
        point_labels: 形状为 [B, 1] 的张量，值为1（前景点）
    """
    # 处理输入格式
    if bbox.dim() == 1:
        bbox = bbox.unsqueeze(0)  # [4] -> [1, 4]

    batch_size = bbox.shape[0]

    # 计算中心点坐标
    x1, y1, x2, y2 = bbox[:, 0], bbox[:, 1], bbox[:, 2], bbox[:, 3]
    center_x = (x1 + x2) / 2.0
    center_y = (y1 + y2) / 2.0

    # 组合中心点坐标 [B, 2]
    center_points = torch.stack([center_x, center_y], dim=1)

    # 如果提供了变换，应用坐标变换
    if sam_transform is not None and original_size is not None:
        # 转换为numpy进行变换
        center_points_np = center_points.cpu().numpy()
        transformed_points_np = sam_transform.apply_coords(center_points_np, original_size)
        center_points = torch.from_numpy(transformed_points_np).to(device=device if device else bbox.device, dtype=torch.float)

    # 调整为SAM期望的格式 [B, 1, 2]
    point_coords = center_points.unsqueeze(1)

    # 创建点标签 [B, 1]，1表示前景点
    point_labels = torch.ones(batch_size, 1, device=point_coords.device, dtype=torch.int)

    return point_coords, point_labels


# ==================== 动态点提示采样功能 ====================

def sample_positive_points(fidt_map, target_mask, num_points=1, device=None):
    """
    基于FIDT图进行正样本点的加权采样

    Args:
        fidt_map: FIDT图 [H, W]，torch.Tensor
        target_mask: 目标掩码 [H, W]，torch.Tensor，二值掩码
        num_points: 采样点数量，默认1
        device: 设备

    Returns:
        point_coords: 采样的点坐标 [num_points, 2] (x, y)
        point_labels: 点标签 [num_points]，全为1（正样本）
    """
    if device is None:
        device = fidt_map.device

    # 确保输入是二值掩码
    target_mask = (target_mask > 0.5).float()

    # 步骤1：计算采样概率图：只在前景区域采样
    prob_map = fidt_map * target_mask

    # 检查是否有有效的前景区域
    if torch.sum(target_mask) == 0:
        # 如果没有前景区域，返回图像中心点
        h, w = fidt_map.shape
        center_x, center_y = w // 2, h // 2
        point_coords = torch.tensor([[center_x, center_y]], dtype=torch.float, device=device)
        point_labels = torch.ones(1, dtype=torch.int, device=device)
        return point_coords, point_labels

    # 步骤2：将概率图展平为一维向量
    prob_flat = prob_map.flatten()

    # 步骤3：使用加权采样
    if torch.sum(prob_flat) == 0:
        # 如果FIDT图全为0，则在前景区域均匀采样
        foreground_indices = torch.nonzero(target_mask.flatten(), as_tuple=False).squeeze(1)
        if len(foreground_indices) == 0:
            # 回退到中心点
            h, w = fidt_map.shape
            center_x, center_y = w // 2, h // 2
            point_coords = torch.tensor([[center_x, center_y]], dtype=torch.float, device=device)
            point_labels = torch.ones(1, dtype=torch.int, device=device)
            return point_coords, point_labels

        # 从前景区域随机采样
        sampled_indices = foreground_indices[torch.randperm(len(foreground_indices))[:num_points]]
    else:
        # 使用FIDT图加权采样
        sampled_indices = torch.multinomial(prob_flat, num_samples=num_points, replacement=True)

    # 步骤4：将采样索引转换回原图坐标
    h, w = fidt_map.shape
    sampled_y = sampled_indices // w
    sampled_x = sampled_indices % w

    # 组合坐标 (x, y)
    point_coords = torch.stack([sampled_x.float(), sampled_y.float()], dim=1)
    point_labels = torch.ones(num_points, dtype=torch.int, device=device)

    return point_coords, point_labels


def sample_negative_points(target_mask, num_points=3, kernel_size=7, device=None):
    """
    在目标掩码的边界外围区域采样负样本点

    Args:
        target_mask: 目标掩码 [H, W]，torch.Tensor，二值掩码
        num_points: 采样点数量，默认3
        kernel_size: 膨胀操作的核大小，默认7
        device: 设备

    Returns:
        point_coords: 采样的点坐标 [num_points, 2] (x, y)
        point_labels: 点标签 [num_points]，全为0（负样本）
    """
    if device is None:
        device = target_mask.device

    # 确保输入是二值掩码
    target_mask = (target_mask > 0.5).float()

    # 步骤1：对目标掩码进行形态学膨胀
    # 使用最大池化实现膨胀操作
    padding = kernel_size // 2
    dilated_mask = F.max_pool2d(
        target_mask.unsqueeze(0).unsqueeze(0),  # [1, 1, H, W]
        kernel_size=kernel_size,
        stride=1,
        padding=padding
    ).squeeze()  # [H, W]

    # 步骤2：计算边界外围区域
    outer_boundary_mask = dilated_mask - target_mask

    # 步骤3：提取候选点坐标
    candidate_coords = torch.nonzero(outer_boundary_mask, as_tuple=False)  # [N, 2] (y, x)

    if len(candidate_coords) == 0:
        # 如果没有边界外围区域，在图像边缘随机采样
        h, w = target_mask.shape
        edge_coords = []
        # 添加图像边缘的点
        for _ in range(num_points * 2):  # 多采样一些以确保有足够的点
            if random.random() < 0.5:
                # 水平边缘
                x = random.randint(0, w-1)
                y = 0 if random.random() < 0.5 else h-1
            else:
                # 垂直边缘
                x = 0 if random.random() < 0.5 else w-1
                y = random.randint(0, h-1)
            edge_coords.append([y, x])

        candidate_coords = torch.tensor(edge_coords, device=device)

    # 步骤4：随机采样
    if len(candidate_coords) <= num_points:
        # 如果候选点不够，全部使用
        selected_coords = candidate_coords
    else:
        # 随机选择指定数量的点
        indices = torch.randperm(len(candidate_coords))[:num_points]
        selected_coords = candidate_coords[indices]

    # 转换坐标格式：(y, x) -> (x, y)
    point_coords = torch.stack([selected_coords[:, 1].float(), selected_coords[:, 0].float()], dim=1)
    point_labels = torch.zeros(len(point_coords), dtype=torch.int, device=device)

    # 如果采样的点数不够，用随机点补充
    while len(point_coords) < num_points:
        h, w = target_mask.shape
        random_x = torch.randint(0, w, (1,), device=device).float()
        random_y = torch.randint(0, h, (1,), device=device).float()
        random_coord = torch.stack([random_x, random_y], dim=1)
        random_label = torch.zeros(1, dtype=torch.int, device=device)

        point_coords = torch.cat([point_coords, random_coord], dim=0)
        point_labels = torch.cat([point_labels, random_label], dim=0)

    return point_coords[:num_points], point_labels[:num_points]


def compute_center_point(target_mask, device=None):
    """
    计算目标掩码的几何中心点（用于测试阶段的确定性采样）

    Args:
        target_mask: 目标掩码 [H, W]，torch.Tensor，二值掩码
        device: 设备

    Returns:
        point_coords: 中心点坐标 [1, 2] (x, y)
        point_labels: 点标签 [1]，值为1（正样本）
    """
    if device is None:
        device = target_mask.device

    # 确保输入是二值掩码
    target_mask = (target_mask > 0.5).float()

    # 检查是否有前景区域
    if torch.sum(target_mask) == 0:
        # 如果没有前景区域，返回图像中心点
        h, w = target_mask.shape
        center_x, center_y = w // 2, h // 2
        point_coords = torch.tensor([[center_x, center_y]], dtype=torch.float, device=device)
        point_labels = torch.ones(1, dtype=torch.int, device=device)
        return point_coords, point_labels

    # 使用距离变换计算几何中心
    # 将tensor转换为numpy进行距离变换
    mask_np = target_mask.cpu().numpy().astype(np.uint8)

    # 计算距离变换
    dist_transform = cv2.distanceTransform(mask_np, cv2.DIST_L2, 5)

    # 找到距离变换图中的最大值点
    max_dist_idx = np.unravel_index(np.argmax(dist_transform), dist_transform.shape)
    center_y, center_x = max_dist_idx

    # 转换回tensor格式
    point_coords = torch.tensor([[center_x, center_y]], dtype=torch.float, device=device)
    point_labels = torch.ones(1, dtype=torch.int, device=device)

    return point_coords, point_labels


def dynamic_point_sampling(fidt_map, target_mask, is_training=True, device=None):
    """
    动态点提示采样的主函数

    Args:
        fidt_map: FIDT图 [H, W]，torch.Tensor
        target_mask: 目标掩码 [H, W]，torch.Tensor
        is_training: 是否为训练模式
        device: 设备

    Returns:
        point_coords: 点坐标 [N, 2] (x, y)
        point_labels: 点标签 [N]
    """
    if device is None:
        device = fidt_map.device

    if is_training:
        # 训练阶段：动态采样1个正样本点 + 3个负样本点
        pos_coords, pos_labels = sample_positive_points(fidt_map, target_mask, num_points=1, device=device)
        neg_coords, neg_labels = sample_negative_points(target_mask, num_points=3, device=device)

        # 合并正负样本点
        point_coords = torch.cat([pos_coords, neg_coords], dim=0)
        point_labels = torch.cat([pos_labels, neg_labels], dim=0)
    else:
        # 测试阶段：使用确定性的中心点
        point_coords, point_labels = compute_center_point(target_mask, device=device)

    return point_coords, point_labels



# 自定义 to_onehot 函数
def to_onehot(labels, num_classes):
    """
    将类别索引张量转换为 one-hot 编码
    
    参数:
        labels: [B, 1, H, W] 或 [B, H, W] 格式的整数张量，包含类别索引
        num_classes: 类别总数
    
    返回:
        one_hot: [B, C, H, W] 格式的张量，其中 C = num_classes
    """
    if labels.ndim == 3:
        # [B, H, W] -> [B, 1, H, W]
        labels = labels.unsqueeze(1)
    
    # 确保是长整型，以便用作索引
    labels = labels.long()
    
    # 创建 one-hot 张量
    batch_size, _, height, width = labels.shape
    one_hot = torch.zeros(batch_size, num_classes, height, width, device=labels.device)
    
    # 使用 scatter_ 填充 one-hot 张量
    # 在索引1（类别维度）上根据labels的值填充1
    one_hot.scatter_(1, labels, 1)
    
    return one_hot

def calculate_class_weights(dataset, num_classes, method='inverse'):
    """
    计算基于类别分布的权重
    """
    if method == 'none':
        logger.info("📊 类别权重: 使用均匀权重 (method=none)")
        return None
        
    class_box_counts = torch.zeros(num_classes)
    logger.info(f"📊 开始计算类别权重 | 方法={method} | 类别数={num_classes} | 样本数={len(dataset)}")
    
    # 获取类别名称列表
    try:
        with open(os.path.join(dataset.data_root, 'dataset_info.json'), 'r') as f:
            dataset_info = json.load(f)
            classes_list = dataset_info['classes']
    except:
        classes_list = ['H', 'LD', 'HD', 'other']
        logger.warning(f"无法读取dataset_info.json，使用默认类别: {classes_list}")
    
    num_samples_processed = 0
    
    for idx, data in enumerate(tqdm(dataset, desc="🔍 统计类别分布")):
        if isinstance(data, dict) and 'boxes_by_class' in data:
            boxes_by_class = data['boxes_by_class']
            num_samples_processed += 1
            
            for class_idx, class_name in enumerate(classes_list):
                if class_name in boxes_by_class:
                    boxes_for_class = boxes_by_class[class_name]
                    if hasattr(boxes_for_class, 'shape'):
                        num_boxes = boxes_for_class.shape[0]
                    elif isinstance(boxes_for_class, (list, tuple)):
                        num_boxes = len(boxes_for_class)
                    else:
                        logger.warning(f"样本 {idx}, 类别 {class_name}: 未知的框数据类型 {type(boxes_for_class)}")
                        num_boxes = 0
                    
                    class_box_counts[class_idx] += num_boxes
                        
        elif isinstance(data, dict) and 'raw_boxes_by_class' in data:
            raw_boxes_by_class = data['raw_boxes_by_class']
            num_samples_processed += 1
            
            for class_idx, class_name in enumerate(classes_list):
                if class_name in raw_boxes_by_class:
                    boxes_for_class = raw_boxes_by_class[class_name]
                    if hasattr(boxes_for_class, 'shape'):
                        num_boxes = boxes_for_class.shape[0]
                    elif isinstance(boxes_for_class, (list, tuple)):
                        num_boxes = len(boxes_for_class)
                    else:
                        num_boxes = 0
                    
                    class_box_counts[class_idx] += num_boxes
        else:
            logger.warning(f"样本 {idx}: 缺少框标注数据")
    
    total_boxes = class_box_counts.sum().item()
    logger.info(f"📊 统计完成 | 有效样本={num_samples_processed} | 总框数={total_boxes}")

    if total_boxes == 0:
        logger.warning("⚠️  未找到任何框标注！使用均匀权重")
        return torch.ones(num_classes)
        
    class_freq = class_box_counts / total_boxes
    logger.info(f"📊 类别频率: {[f'{f:.4f}' for f in class_freq.tolist()]}")
    
    if method == 'inverse':
        class_weights = 1.0 / (class_freq + 1e-8)
        class_weights = class_weights * (num_classes / class_weights.sum().item())
        logger.info(f"📊 权重计算完成 | 方法=inverse | 权重={[f'{w:.4f}' for w in class_weights.tolist()]}")
    elif method == 'effective':
        beta = 0.9999
        effective_num = 1.0 - torch.pow(beta, class_box_counts)
        class_weights = (1.0 - beta) / (effective_num + 1e-8)
        class_weights = class_weights * (num_classes / class_weights.sum().item())
        logger.info(f"📊 权重计算完成 | 方法=effective | beta={beta} | 权重={[f'{w:.4f}' for w in class_weights.tolist()]}")
    else:
        logger.error(f"❌ 未知的权重计算方法: {method}")
        raise ValueError(f"未知的权重计算方法: {method}")
    
    return class_weights

def calculate_box_level_class_weights(dataset, classes_list, method='inverse', use_full_dataset=True):
    """
    基于框数量计算类别权重 - 超快速版本
    专门为单框训练设计，直接从元数据文件统计
    """
    if method == 'none':
        return None
        
    print(f"📊 开始计算类别权重 (方法: {method})")
    class_box_counts = torch.zeros(len(classes_list))
    
    try:
        # 方法1: 如果数据集有内置统计方法，优先使用
        if hasattr(dataset, 'get_class_distribution'):
            class_distribution = dataset.get_class_distribution()
            for class_name, count in class_distribution.items():
                if class_name in classes_list:
                    class_idx = classes_list.index(class_name)
                    class_box_counts[class_idx] = count
            print(f"📊 使用数据集内置分布统计")
        
        # 方法2: 直接从元数据文件统计（最快）
        elif hasattr(dataset, 'metadata_files') and dataset.metadata_files:
            print(f"📊 直接从元数据文件统计 ({len(dataset.metadata_files)} 个文件)")
            import json
            
            for metadata_file in dataset.metadata_files[:50]:  # 限制处理文件数量
                try:
                    with open(metadata_file, 'r') as f:
                        metadata = json.load(f)
                    
                    for box_info in metadata.get('boxes_info', []):
                        class_name = box_info.get('class_name', 'other')
                        if class_name in classes_list:
                            class_idx = classes_list.index(class_name)
                            class_box_counts[class_idx] += 1
                except:
                    continue
            
            # 如果只处理了部分文件，按比例缩放
            if len(dataset.metadata_files) > 50:
                scale_factor = len(dataset.metadata_files) / 50
                class_box_counts *= scale_factor
                print(f"📊 元数据统计完成，缩放系数: {scale_factor:.2f}")
            else:
                print(f"📊 元数据统计完成")
        
        # 方法3: 极快速采样（500个样本）
        else:
            sample_size = min(500, len(dataset))  # 大幅减少采样数量
            print(f"📊 极快速采样 ({sample_size}/{len(dataset)} 个框)")
            
            # 使用均匀间隔采样，避免随机采样的开销
            step = max(1, len(dataset) // sample_size)
            sample_indices = list(range(0, len(dataset), step))[:sample_size]
            
            processed_count = 0
            for idx in sample_indices:
                try:
                    data = dataset[idx]
                    class_id = data.get('class_id')
                    if class_id is not None:
                        if isinstance(class_id, torch.Tensor):
                            class_id = class_id.item()
                        if isinstance(class_id, (int, float)) and 0 <= class_id < len(classes_list):
                            class_id = int(class_id)  # 确保是整数类型
                            class_box_counts[class_id] += 1
                            processed_count += 1
                except:
                    continue
                
                # 进度显示，避免用户等待焦虑
                if processed_count % 100 == 0:
                    print(f"  已处理 {processed_count}/{sample_size} 个样本...")
            
            # 缩放到全数据集
            if processed_count > 0:
                scale_factor = len(dataset) / processed_count
                class_box_counts *= scale_factor
                print(f"📊 采样完成，处理 {processed_count} 个框，缩放系数 {scale_factor:.2f}")
            else:
                print("⚠️ 采样失败，使用均匀权重")
                return torch.ones(len(classes_list))
            
    except Exception as e:
        print(f"⚠️ 权重计算失败: {e}，使用均匀权重")
        return torch.ones(len(classes_list))
    
    total_boxes = class_box_counts.sum().item()
    
    if total_boxes == 0:
        print("⚠️ 未找到任何框！使用均匀权重")
        return torch.ones(len(classes_list))
    
    # 计算类别频率和权重
    class_freq = class_box_counts / total_boxes
    
    print(f"📊 类别分布 | 总框数={int(total_boxes)}")
    for i, class_name in enumerate(classes_list):
        print(f"  {class_name}: {int(class_box_counts[i])} 框 ({class_freq[i]:.2%})")
    
    if method == 'inverse':
        class_weights = 1.0 / (class_freq + 1e-8)
        class_weights = class_weights * (len(classes_list) / class_weights.sum().item())
    elif method == 'effective':
        beta = 0.9999
        effective_num = 1.0 - torch.pow(beta, class_box_counts)
        class_weights = (1.0 - beta) / (effective_num + 1e-8)
        class_weights = class_weights * (len(classes_list) / class_weights.sum().item())
    else:
        raise ValueError(f"未知的权重计算方法: {method}")
    
    print(f"📊 计算完成！权重: {[f'{w:.4f}' for w in class_weights.tolist()]}")
    
    return class_weights

class GradientReverseFunction(torch.autograd.Function):
    """
    梯度反转函数的实现
    在前向传播时保持输入不变，在反向传播时反转梯度
    """
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.alpha = alpha
        return x.view_as(x)

    @staticmethod
    def backward(ctx, grad_output):
        return grad_output.neg() * ctx.alpha, None

class GradientReverseLayer(nn.Module):
    """
    梯度反转层，用于域适应
    在反向传播时将梯度反转，使特征提取器学习域不变的特征
    """
    def __init__(self, alpha=1.0):
        super(GradientReverseLayer, self).__init__()
        self.alpha = alpha

    def forward(self, x):
        return GradientReverseFunction.apply(x, self.alpha)

# 🚀 新增：Straight-Through Estimator 实现
class StraightThroughBinarize(torch.autograd.Function):
    """
    Straight-Through Estimator for binary thresholding
    基于 "Rethinking Binary Decisions in Deep Learning" (ICLR 2021)
    """
    @staticmethod
    def forward(ctx, input, threshold):
        """
        前向传播：真实的二值化操作
        """
        ctx.save_for_backward(input, threshold)
        return (input > threshold).float()
    
    @staticmethod
    def backward(ctx, grad_output):
        """
        后向传播：使用平滑近似的梯度
        使用 sigmoid 的导数作为 STE 的梯度近似
        """
        input, threshold = ctx.saved_tensors
        
        # 使用 sigmoid 函数的导数作为梯度近似
        # 这里使用较大的 k 值使得近似更接近真实的阶跃函数
        k = 10.0  # 陡峭度参数
        sigmoid_approx = torch.sigmoid(k * (input - threshold))
        grad_input = grad_output * k * sigmoid_approx * (1 - sigmoid_approx)
        
        # 阈值的梯度（如果阈值是可学习的）
        grad_threshold = -grad_input.sum() if threshold.requires_grad else None
        
        return grad_input, grad_threshold

class LearnableThresholdModule(nn.Module):
    """
    可学习阈值模块
    基于 "Learnable Threshold Networks for Weakly-Supervised Segmentation" (MIDL 2020)
    """
    def __init__(self, num_classes=1, init_threshold=0.5, learn_threshold=True):
        super(LearnableThresholdModule, self).__init__()
        self.num_classes = num_classes
        self.learn_threshold = learn_threshold
        
        if learn_threshold:
            # 可学习的阈值参数，每个类别一个阈值
            self.threshold = nn.Parameter(
                torch.full((num_classes,), init_threshold, dtype=torch.float32)
            )
        else:
            # 固定阈值
            self.register_buffer('threshold', 
                               torch.full((num_classes,), init_threshold, dtype=torch.float32))
    
    def forward(self, input, class_id=None):
        """
        使用 STE 进行二值化
        
        Args:
            input: 输入概率图 [H, W] 或 [B, H, W]
            class_id: 类别ID，用于选择对应的阈值
        
        Returns:
            二值化后的掩码
        """
        if class_id is not None and self.num_classes > 1:
            # 使用特定类别的阈值
            threshold = self.threshold[class_id]
        else:
            # 使用第一个（或唯一的）阈值
            threshold = self.threshold[0] if self.num_classes > 1 else self.threshold
        
        return StraightThroughBinarize.apply(input, threshold)
    
    def get_thresholds(self):
        """获取当前阈值"""
        return self.threshold.detach().cpu().numpy()
    
    def set_thresholds(self, new_thresholds):
        """设置新的阈值"""
        if isinstance(new_thresholds, (list, tuple)):
            new_thresholds = torch.tensor(new_thresholds, dtype=torch.float32)
        
        if self.learn_threshold:
            self.threshold.data = new_thresholds.to(self.threshold.device)
        else:
            self.threshold = new_thresholds.to(self.threshold.device)

# 添加自定义 IoU 计算函数
def calculate_iou(pred, target):
    """
    计算预测和目标之间的IoU (Intersection over Union)。
    
    Args:
        pred: 二值预测掩码 [B, C, H, W]，其中C是类别数 (或者 [C, H, W] 如果B=1)
        target: 二值目标掩码，与pred形状相同
    
    Returns:
        iou_scores: 每个类别的IoU得分张量 [C]
    """
    if pred.dim() == 3:  # 单样本，添加批次维度
        pred = pred.unsqueeze(0)
    if target.dim() == 3:
        target = target.unsqueeze(0)
    
    # 确保输入是二值的（通过四舍五入/阈值处理）
    pred = (pred > 0.5).float()
    target = (target > 0.5).float()
    
    # 计算交集和并集
    intersection = torch.sum(pred * target, dim=(0, 2, 3)) # 沿 B, H, W 维度求和，保留 C 维度
    union = torch.sum(pred, dim=(0, 2, 3)) + torch.sum(target, dim=(0, 2, 3)) - intersection
    
    # 避免除零错误 (where union = 0)
    iou_scores = torch.zeros_like(intersection)
    valid_mask = union > 0
    iou_scores[valid_mask] = intersection[valid_mask] / union[valid_mask]
    
    return iou_scores

class IoUMetric:
    """
    自定义IoU计算指标，接口与MONAI的DiceMetric类似。
    """
    def __init__(self, include_background=True, reduction="mean", get_not_nans=False):
        self.include_background = include_background
        self.reduction = reduction
        self.get_not_nans = get_not_nans
        self._vals = []
    
    def __call__(self, y_pred, y):
        """
        Args:
            y_pred: 预测的分割掩码，[B, C, H, W] 格式
            y: 目标分割掩码，[B, C, H, W] 格式
        """
        if not self.include_background:
            # 假设背景是第一个通道 (索引0)
            y_pred = y_pred[:, 1:] if y_pred.shape[1] > 1 else y_pred
            y = y[:, 1:] if y.shape[1] > 1 else y
        
        iou_vals = calculate_iou(y_pred, y)
        
        # 保存当前批次的结果
        self._vals.append(iou_vals)
        
        return iou_vals
    
    def aggregate(self):
        """
        聚合所有批次的结果。
        
        Returns:
            根据reduction返回聚合后的IoU值，格式为 [C] (per class)
        """
        if not self._vals:
            return torch.tensor(0.0)
        
        # 将所有批次的结果堆叠起来
        vals = torch.stack(self._vals, dim=0)  # [batches, classes]
        
        if self.reduction == "mean":
            # 沿批次维度求平均
            return torch.mean(vals, dim=0)  # [classes]
        elif self.reduction == "sum":
            return torch.sum(vals, dim=0)  # [classes]
        else:  # No reduction
            return vals
    
    def reset(self):
        """重置保存的值。"""
        self._vals = []

class SafeConfusionMatrixMetric:
    """
    安全的混淆矩阵度量包装器，基于MONAI的ConfusionMatrixMetric
    处理异常值并确保结果在合理范围内
    """
    def __init__(self, include_background=True, metric_name=None, reduction="mean", get_not_nans=False):
        self.include_background = include_background
        self.reduction = reduction
        self.get_not_nans = get_not_nans
        
        if metric_name is None:
            metric_name = ["accuracy", "precision", "recall", "f1_score"]
        self.metric_name = metric_name
        
        # 创建MONAI的原生混淆矩阵指标实例
        self.monai_cm_metric = ConfusionMatrixMetric(
            include_background=include_background,
            metric_name=metric_name,
            reduction="none",  # 我们手动处理聚合
            get_not_nans=get_not_nans
        )
        
        self._vals = []
        
    def __call__(self, y_pred, y):
        """
        Args:
            y_pred: 预测的类别索引，[B, H, W] 格式
            y: 目标类别索引，[B, H, W] 格式
        """
        if not self.include_background:
            # 需要将背景类别（0）的预测和目标调整为其他值，然后重新映射
            # 这比较复杂，暂时保持原样，但在后处理中处理
            pass
        
        # 使用安全的混淆矩阵计算
        cm_vals = self._compute_safe_confusion_matrix_with_monai(y_pred, y)
        
        # 保存当前批次的结果
        self._vals.append(cm_vals)
        
        return cm_vals
    
    def _compute_safe_confusion_matrix_with_monai(self, y_pred, y):
        """
        使用MONAI的ConfusionMatrixMetric，但后处理异常值
        """
        try:
            # 调用MONAI的混淆矩阵计算
            cm_result = self.monai_cm_metric(y_pred, y)
            
            # 检查并修正异常值
            if torch.is_tensor(cm_result):
                # 对于precision，确保值在[0, 1]范围内
                if "precision" in self.metric_name:
                    precision_idx = self.metric_name.index("precision")
                    if cm_result.dim() == 3:  # [B, C, N_metrics]
                        # 检查精度值
                        precision_values = cm_result[:, :, precision_idx]
                        # 将异常值（>1或<0或nan/inf）设为NaN
                        invalid_mask = (precision_values > 1.0) | (precision_values < 0.0) | torch.isnan(precision_values) | torch.isinf(precision_values)
                        cm_result[:, :, precision_idx] = torch.where(invalid_mask, float('nan'), precision_values)
                
                # 对于recall，确保值在[0, 1]范围内
                if "recall" in self.metric_name:
                    recall_idx = self.metric_name.index("recall")
                    if cm_result.dim() == 3:
                        recall_values = cm_result[:, :, recall_idx]
                        invalid_mask = (recall_values > 1.0) | (recall_values < 0.0) | torch.isnan(recall_values) | torch.isinf(recall_values)
                        cm_result[:, :, recall_idx] = torch.where(invalid_mask, float('nan'), recall_values)
                
                # 对于f1_score，确保值在[0, 1]范围内
                if "f1_score" in self.metric_name:
                    f1_idx = self.metric_name.index("f1_score")
                    if cm_result.dim() == 3:
                        f1_values = cm_result[:, :, f1_idx]
                        invalid_mask = (f1_values > 1.0) | (f1_values < 0.0) | torch.isnan(f1_values) | torch.isinf(f1_values)
                        cm_result[:, :, f1_idx] = torch.where(invalid_mask, float('nan'), f1_values)
                
                # 对于accuracy，确保值在[0, 1]范围内
                if "accuracy" in self.metric_name:
                    acc_idx = self.metric_name.index("accuracy")
                    if cm_result.dim() == 3:
                        acc_values = cm_result[:, :, acc_idx]
                        invalid_mask = (acc_values > 1.0) | (acc_values < 0.0) | torch.isnan(acc_values) | torch.isinf(acc_values)
                        cm_result[:, :, acc_idx] = torch.where(invalid_mask, float('nan'), acc_values)
                        
                return cm_result
            else:
                # 如果不是张量，创建NaN填充的结果
                device = y_pred.device if hasattr(y_pred, 'device') else 'cpu'
                batch_size = y_pred.shape[0] if hasattr(y_pred, 'shape') else 1
                num_classes = len(torch.unique(y)) if torch.is_tensor(y) else 4  # 默认4个类别
                return torch.full((batch_size, num_classes, len(self.metric_name)), float('nan'), device=device)
                
        except Exception as e:
            logger.warning(f"MONAI混淆矩阵计算失败: {e}，返回NaN值")
            # 如果MONAI计算出错，返回NaN填充的结果
            device = y_pred.device if hasattr(y_pred, 'device') else 'cpu'
            batch_size = y_pred.shape[0] if hasattr(y_pred, 'shape') else 1
            num_classes = len(torch.unique(y)) if torch.is_tensor(y) else 4
            return torch.full((batch_size, num_classes, len(self.metric_name)), float('nan'), device=device)
    
    def aggregate(self):
        """
        聚合所有批次的结果
        """
        if not self._vals:
            return torch.tensor(0.0)
        
        # 将所有批次的结果堆叠起来
        vals = torch.stack(self._vals, dim=0)  # [batches, batch_size, classes, metrics]
        
        if self.reduction == "mean":
            # 先沿批次和批次内样本维度求平均
            return torch.nanmean(vals, dim=(0, 1))  # [classes, metrics]
        elif self.reduction == "sum":
            return torch.nansum(vals, dim=(0, 1))  # [classes, metrics]
        else:  # No reduction
            return vals
    
    def reset(self):
        """重置保存的值。"""
        self._vals = []
        # 同时重置内部的MONAI指标
        if hasattr(self.monai_cm_metric, 'reset'):
            self.monai_cm_metric.reset()

class SafeHausdorffDistanceMetric:
    """
    安全的Hausdorff距离度量包装器，基于MONAI的HausdorffDistanceMetric
    通过预处理避免全0类别产生的警告
    """
    def __init__(self, include_background=True, reduction="mean", get_not_nans=False, percentile=95):
        self.include_background = include_background
        self.reduction = reduction
        self.get_not_nans = get_not_nans
        self.percentile = percentile
        
        # 创建MONAI的原生HD指标实例
        self.monai_hd_metric = HausdorffDistanceMetric(
            include_background=include_background,
            reduction="none",  # 我们手动处理聚合
            get_not_nans=get_not_nans,
            percentile=percentile,
            directed=False  # 计算双向HD距离
        )
        
        self._vals = []
        
    def __call__(self, y_pred, y):
        """
        Args:
            y_pred: 预测的分割掩码，[B, C, H, W] 格式
            y: 目标分割掩码，[B, C, H, W] 格式
        """
        if not self.include_background:
            # 排除背景类别（第一个通道）
            y_pred = y_pred[:, 1:] if y_pred.shape[1] > 1 else y_pred
            y = y[:, 1:] if y.shape[1] > 1 else y
        
        # 使用预处理的安全HD计算
        hd_vals = self._compute_safe_hausdorff_with_monai(y_pred, y)
        
        # 保存当前批次的结果
        self._vals.append(hd_vals)
        
        return hd_vals
    
    def _compute_safe_hausdorff_with_monai(self, y_pred, y):
        """
        使用MONAI的HausdorffDistanceMetric，但预处理全0类别以避免警告
        """
        batch_size, num_classes = y_pred.shape[0], y_pred.shape[1]
        device = y_pred.device
        
        # 为每个类别和每个批次样本检查是否全0
        results = torch.full((batch_size, num_classes), float('nan'), 
                           device=device, dtype=torch.float32)
        
        # 分类别处理以避免警告
        for c in range(num_classes):
            # 提取当前类别的掩码
            pred_class = y_pred[:, c:c+1, :, :]  # [B, 1, H, W]
            target_class = y[:, c:c+1, :, :]     # [B, 1, H, W]
            
            # 检查每个批次样本的当前类别是否全0
            for b in range(batch_size):
                pred_sum = torch.sum(pred_class[b])
                target_sum = torch.sum(target_class[b])
                
                if pred_sum == 0 and target_sum == 0:
                    # 预测和真实都为0：完美匹配，距离为0
                    results[b, c] = 0.0
                elif pred_sum == 0 or target_sum == 0:
                    # 其中一个为0：返回NaN，在聚合时忽略
                    results[b, c] = float('nan')
                else:
                    # 两个掩码都有前景像素：使用MONAI计算
                    try:
                        # 创建单个样本、单个类别的张量用于MONAI计算
                        single_pred = pred_class[b:b+1]  # [1, 1, H, W]
                        single_target = target_class[b:b+1]  # [1, 1, H, W]
                        
                        # 抑制MONAI的警告（额外保险）
                        import warnings
                        with warnings.catch_warnings():
                            warnings.filterwarnings("ignore", "the ground truth.*is all 0.*may result in nan/inf distance")
                            warnings.filterwarnings("ignore", "the prediction.*is all 0.*may result in nan/inf distance")
                            
                            # 调用MONAI的HD计算
                            hd_result = self.monai_hd_metric(single_pred, single_target)
                            
                            # 提取结果
                            if torch.is_tensor(hd_result) and hd_result.numel() > 0:
                                hd_val = hd_result[0, 0] if hd_result.dim() > 1 else hd_result[0]
                                results[b, c] = hd_val if not torch.isnan(hd_val) and not torch.isinf(hd_val) else float('nan')
                            else:
                                results[b, c] = float('nan')
                                
                    except Exception as e:
                        # 如果MONAI计算出错，返回NaN
                        results[b, c] = float('nan')
        
        return results
    
    def aggregate(self):
        """
        聚合所有批次的结果
        """
        if not self._vals:
            return torch.tensor(0.0)
        
        # 将所有批次的结果堆叠起来
        vals = torch.stack(self._vals, dim=0)  # [batches, batch_size, classes]
        
        if self.reduction == "mean":
            # 先沿批次和批次内样本维度求平均
            return torch.nanmean(vals, dim=(0, 1))  # [classes]
        elif self.reduction == "sum":
            return torch.nansum(vals, dim=(0, 1))  # [classes]
        else:  # No reduction
            return vals
    
    def reset(self):
        """重置保存的值。"""
        self._vals = []
        # 同时重置内部的MONAI指标
        if hasattr(self.monai_hd_metric, 'reset'):
            self.monai_hd_metric.reset()

def calculate_per_box_metrics(pred_mask, target_mask, class_id, debug=False, bbox_mask=None, ste_module=None):
    """
    为单个框计算增强的完整指标集
    🚀 修复版：支持bbox_mask，避免背景稀释；支持STE可学习阈值
    
    Args:
        pred_mask: 预测掩码 (torch.Tensor)
        target_mask: 目标掩码 (torch.Tensor) - 已经应用了bbox_mask
        class_id: 类别ID
        debug: 是否启用调试模式
        bbox_mask: 边界框掩码 (可选，如果target_mask已经是ROI则无需传入)
        ste_module: STE模块实例 (可选，用于可学习阈值)
    
    Returns:
        dict: 包含所有增强指标的字典
    """
    # 🚀 如果提供了bbox_mask，进一步确保只在框内计算
    if bbox_mask is not None:
        pred_mask = pred_mask * bbox_mask
        target_mask = target_mask * bbox_mask
    
    # 🚀 转换为二值掩码：使用STE或固定阈值
    if ste_module is not None:
        # 使用可学习阈值的STE
        pred_binary = ste_module(pred_mask, class_id)
        if debug and class_id is not None:
            current_threshold = ste_module.get_thresholds()[class_id] if ste_module.num_classes > 1 else ste_module.get_thresholds()[0]
            print(f"[STE Debug] Class {class_id}: 当前阈值={current_threshold:.4f}")
    else:
        # 使用固定阈值 (回退到原始方法)
        pred_binary = (pred_mask > 0.5).float()
    
    target_binary = (target_mask > 0.5).float()
    
    # 获取掩码统计信息用于调试
    pred_sum = torch.sum(pred_binary).item()
    target_sum = torch.sum(target_binary).item()
    
    if debug:
        print(f"[MetricsDebug] Class {class_id}: pred_pixels={pred_sum}, target_pixels={target_sum}")
        if bbox_mask is not None:
            bbox_pixels = torch.sum(bbox_mask).item()
            print(f"[MetricsDebug] Class {class_id}: bbox_pixels={bbox_pixels}")
    
    metrics = {}
    
    # ========== 基础分割指标 ==========
    # Dice系数
    intersection = torch.sum(pred_binary * target_binary)
    union = torch.sum(pred_binary) + torch.sum(target_binary)
    metrics['dice'] = (2.0 * intersection / (union + 1e-8)).item()
    
    # IoU (Intersection over Union)
    union_iou = torch.sum(pred_binary) + torch.sum(target_binary) - intersection
    metrics['iou'] = (intersection / (union_iou + 1e-8)).item()
    
    # Surface Dice（边界Dice）
    metrics['surface_dice'] = calculate_surface_dice(pred_binary, target_binary)
    
    # Boundary IoU（边界IoU）
    metrics['boundary_iou'] = calculate_boundary_iou(pred_binary, target_binary)
    
    # ========== HD95距离指标（修复版） ==========
    metrics['hd95'] = calculate_safe_hd95(pred_binary, target_binary, debug=debug)
    
    # ========== 混淆矩阵衍生指标 ==========
    # 基础混淆矩阵元素
    tp = intersection.item()
    fp = (torch.sum(pred_binary) - intersection).item()
    fn = (torch.sum(target_binary) - intersection).item()
    tn = (pred_binary.numel() - tp - fp - fn)  # 真负样本
    
    # Precision, Recall, F1-Score
    metrics['precision'] = tp / (tp + fp + 1e-8)
    metrics['recall'] = tp / (tp + fn + 1e-8)
    metrics['f1'] = 2 * tp / (2 * tp + fp + fn + 1e-8)
    
    # Specificity (特异性)
    metrics['specificity'] = tn / (tn + fp + 1e-8)
    
    # Accuracy (像素级准确率)
    metrics['accuracy'] = (tp + tn) / (tp + tn + fp + fn + 1e-8)
    
    # Balanced Accuracy (平衡准确率)
    sensitivity = metrics['recall']  # 敏感性 = 召回率
    specificity = metrics['specificity']
    metrics['balanced_accuracy'] = (sensitivity + specificity) / 2.0
    
    # ========== 其他有用指标 ==========
    # Jaccard Index (等价于IoU，但明确标出)
    metrics['jaccard'] = metrics['iou']
    
    # 假阳性率 (FPR)
    metrics['fpr'] = fp / (fp + tn + 1e-8)
    
    # 假阴性率 (FNR)
    metrics['fnr'] = fn / (fn + tp + 1e-8)
    
    if debug:
        print(f"[MetricsDebug] Class {class_id}: dice={metrics['dice']:.4f}, iou={metrics['iou']:.4f}, hd95={metrics['hd95']:.2f}")
    
    return metrics

def calculate_surface_dice(pred_binary, target_binary, tolerance=1.0):
    """
    计算Surface Dice（边界Dice）用于评估边缘预测质量
    """
    try:
        # 获取边界
        pred_boundary = get_boundary_mask(pred_binary)
        target_boundary = get_boundary_mask(target_binary)
        
        if torch.sum(pred_boundary) == 0 and torch.sum(target_boundary) == 0:
            return 1.0  # 两个都没有边界，完美匹配
        
        if torch.sum(pred_boundary) == 0 or torch.sum(target_boundary) == 0:
            return 0.0  # 其中一个没有边界
        
        # 计算边界距离
        pred_coords = torch.nonzero(pred_boundary, as_tuple=False).float()
        target_coords = torch.nonzero(target_boundary, as_tuple=False).float()
        
        if len(pred_coords) == 0 or len(target_coords) == 0:
            return 0.0
        
        # 计算距离矩阵
        distances = torch.cdist(pred_coords, target_coords)
        
        # 计算在容忍度内的匹配
        pred_matches = torch.min(distances, dim=1)[0] <= tolerance
        target_matches = torch.min(distances, dim=0)[0] <= tolerance
        
        # Surface Dice
        surface_dice = (torch.sum(pred_matches).float() + torch.sum(target_matches).float()) / (len(pred_coords) + len(target_coords))
        return surface_dice.item()
        
    except Exception as e:
        return float('nan')

def calculate_boundary_iou(pred_binary, target_binary, dilation_radius=2):
    """
    计算Boundary IoU（边界IoU）
    """
    try:
        # 获取膨胀的边界区域
        pred_boundary = get_dilated_boundary(pred_binary, dilation_radius)
        target_boundary = get_dilated_boundary(target_binary, dilation_radius)
        
        # 计算边界IoU
        boundary_intersection = torch.sum(pred_boundary * target_boundary)
        boundary_union = torch.sum(pred_boundary) + torch.sum(target_boundary) - boundary_intersection
        
        if boundary_union == 0:
            return 1.0 if boundary_intersection == 0 else 0.0
        
        return (boundary_intersection / boundary_union).item()
        
    except Exception as e:
        return float('nan')

def get_boundary_mask(binary_mask):
    """
    获取二值掩码的边界
    """
    # 使用形态学操作获取边界
    kernel = torch.ones(3, 3, device=binary_mask.device)
    
    # 膨胀
    dilated = F.max_pool2d(binary_mask.unsqueeze(0).unsqueeze(0), 
                          kernel_size=3, stride=1, padding=1).squeeze()
    
    # 腐蚀
    eroded = -F.max_pool2d(-binary_mask.unsqueeze(0).unsqueeze(0), 
                          kernel_size=3, stride=1, padding=1).squeeze()
    
    # 边界 = 膨胀 - 腐蚀
    boundary = dilated - eroded
    return boundary > 0

def get_dilated_boundary(binary_mask, radius=2):
    """
    获取膨胀的边界区域
    """
    boundary = get_boundary_mask(binary_mask)
    
    # 膨胀边界
    kernel_size = 2 * radius + 1
    dilated_boundary = F.max_pool2d(boundary.float().unsqueeze(0).unsqueeze(0), 
                                   kernel_size=kernel_size, stride=1, 
                                   padding=radius).squeeze()
    
    return dilated_boundary > 0

def calculate_safe_hd95(pred_binary, target_binary, debug=False):
    """
    安全的HD95计算，修复空掩码处理和调试信息
    实现方案1(1)：修复HD95指标计算错误
    """
    try:
        pred_sum = torch.sum(pred_binary).item()
        target_sum = torch.sum(target_binary).item()
        
        # 处理边界情况
        if pred_sum == 0 and target_sum == 0:
            if debug:
                print("[HD95Debug] 预测和目标都为空，返回0.0")
            return 0.0  # 完美匹配
        
        if pred_sum == 0:
            if debug:
                print(f"[HD95Debug] 预测为空但目标有{target_sum}像素，返回NaN")
            return float('nan')  # 预测为空但目标不为空
        
        if target_sum == 0:
            if debug:
                print(f"[HD95Debug] 目标为空但预测有{pred_sum}像素，返回NaN")
            return float('nan')  # 目标为空但预测不为空
        
        # 使用MONAI计算HD95
        if MONAI_AVAILABLE:
            # 转换为one-hot格式 [B, C, H, W]
            pred_4d = pred_binary.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
            target_4d = target_binary.unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
            
            # 创建二分类的one-hot编码
            pred_onehot = torch.cat([1 - pred_4d, pred_4d], dim=1)  # [1, 2, H, W]
            target_onehot = torch.cat([1 - target_4d, target_4d], dim=1)  # [1, 2, H, W]
            
            hd_metric = SafeHausdorffDistanceMetric(include_background=False, percentile=95)
            hd_result = hd_metric(pred_onehot, target_onehot)
            
            if torch.is_tensor(hd_result) and hd_result.numel() > 0:
                hd_value = hd_result[0].item() if hd_result.dim() > 0 else hd_result.item()
                if debug:
                    print(f"[HD95Debug] MONAI计算结果: {hd_value:.2f}")
                return hd_value if not (np.isnan(hd_value) or np.isinf(hd_value)) else float('nan')
            else:
                if debug:
                    print("[HD95Debug] MONAI返回空结果，返回NaN")
                return float('nan')
        else:
            # 简单的欧氏距离HD95近似
            return calculate_simple_hd95(pred_binary, target_binary, debug=debug)
            
    except Exception as e:
        if debug:
            print(f"[HD95Debug] 计算失败: {e}")
        return float('nan')

def calculate_simple_hd95(pred_binary, target_binary, debug=False):
    """
    简单的HD95近似计算（不依赖MONAI）
    """
    try:
        # 获取边界点
        pred_coords = torch.nonzero(pred_binary, as_tuple=False).float()
        target_coords = torch.nonzero(target_binary, as_tuple=False).float()
        
        if len(pred_coords) == 0 or len(target_coords) == 0:
            return float('nan')
        
        # 限制点数以避免内存爆炸
        max_points = 1000
        if len(pred_coords) > max_points:
            indices = torch.randperm(len(pred_coords))[:max_points]
            pred_coords = pred_coords[indices]
        
        if len(target_coords) > max_points:
            indices = torch.randperm(len(target_coords))[:max_points]
            target_coords = target_coords[indices]
        
        # 计算距离
        distances_p2t = torch.cdist(pred_coords, target_coords)
        distances_t2p = torch.cdist(target_coords, pred_coords)
        
        # 最近邻距离
        min_dist_p2t = torch.min(distances_p2t, dim=1)[0]
        min_dist_t2p = torch.min(distances_t2p, dim=1)[0]
        
        # 合并所有距离
        all_distances = torch.cat([min_dist_p2t, min_dist_t2p])
        
        # 计算95%分位数
        k = int(0.95 * len(all_distances))
        hd95_value = torch.kthvalue(all_distances, k)[0].item()
        
        if debug:
            print(f"[HD95Debug] 简单计算结果: {hd95_value:.2f}")
        
        return hd95_value
        
    except Exception as e:
        if debug:
            print(f"[HD95Debug] 简单计算失败: {e}")
        return float('nan')

def aggregate_metrics_across_boxes(per_box_metrics_list, classes_list):
    """
    聚合所有框的增强指标集
    实现方案1(4)：重构指标聚合系统，支持总体指标和按类别指标的分别计算
    
    Args:
        per_box_metrics_list: list of dicts with 'class_name' and 'metrics' keys
        classes_list: 类别名称列表
    
    Returns:
        dict: {
            'overall': {...},    # 所有框的平均指标
            'per_class': {...},  # 每个类别的平均指标
            'summary': {...}     # 汇总统计信息
        }
    """
    if not per_box_metrics_list:
        return {'overall': {}, 'per_class': {}, 'summary': {}}
    
    # 按类别分组
    metrics_by_class = {cls: [] for cls in classes_list}
    all_metrics = []
    
    for box_metrics in per_box_metrics_list:
        class_name = box_metrics['class_name']
        metrics = box_metrics['metrics']
        
        if class_name in metrics_by_class:
            metrics_by_class[class_name].append(metrics)
            all_metrics.append(metrics)
    
    # 增强的指标列表（包含所有新增指标）
    metric_names = [
        # 基础分割指标
        'dice', 'iou', 'jaccard',
        # 边界相关指标
        'surface_dice', 'boundary_iou', 'hd95',
        # 混淆矩阵衍生指标
        'precision', 'recall', 'f1', 'specificity', 'accuracy', 'balanced_accuracy',
        # 其他指标
        'fpr', 'fnr'
    ]
    
    # 计算整体平均
    overall_avg = {}
    for metric_name in metric_names:
        values = [m.get(metric_name, float('nan')) for m in all_metrics]
        valid_values = [v for v in values if not np.isnan(v)]
        overall_avg[metric_name] = np.mean(valid_values) if valid_values else 0.0
    
    # 计算每类别平均
    per_class_avg = {}
    for class_name, class_metrics in metrics_by_class.items():
        if class_metrics:
            class_avg = {}
            for metric_name in metric_names:
                values = [m.get(metric_name, float('nan')) for m in class_metrics]
                valid_values = [v for v in values if not np.isnan(v)]
                class_avg[metric_name] = np.mean(valid_values) if valid_values else 0.0
            per_class_avg[class_name] = class_avg
        else:
            per_class_avg[class_name] = {metric: 0.0 for metric in metric_names}
    
    # 生成汇总统计信息
    summary = {
        'total_boxes': len(per_box_metrics_list),
        'boxes_per_class': {cls: len(metrics_by_class[cls]) for cls in classes_list},
        'valid_metrics_count': {},  # 每个指标的有效值数量
        'mean_metrics': overall_avg.copy(),  # 平均指标值
        'top_performing_class': {},  # 每个指标表现最佳的类别
    }
    
    # 计算每个指标的有效值数量
    for metric_name in metric_names:
        valid_count = sum(1 for m in all_metrics if not np.isnan(m.get(metric_name, float('nan'))))
        summary['valid_metrics_count'][metric_name] = valid_count
    
    # 找出每个指标表现最佳的类别
    for metric_name in metric_names:
        best_class = None
        best_value = -1
        for class_name, class_avg in per_class_avg.items():
            class_value = class_avg.get(metric_name, 0.0)
            if not np.isnan(class_value) and class_value > best_value:
                best_value = class_value
                best_class = class_name
        summary['top_performing_class'][metric_name] = {
            'class': best_class,
            'value': best_value
        }
    
    return {
        'overall': overall_avg,
        'per_class': per_class_avg,
        'summary': summary
    }

def calculate_and_log_segmentation_metrics(
    epoch, 
    dice_metric_instance, 
    iou_metric_instance, 
    hd_metric_instance, 
    confusion_matrix_metric_instance,
    conf_matrix_metric_names_list,
    writer_instance, 
    classes_list,
    logger_instance
):
    """
    聚合、记录分割评估指标，并重置指标状态。
    """
    try:
        logger_instance.debug(f"📏 Epoch {epoch+1}: 开始聚合评估指标")
        
        # 聚合所有指标
        epoch_dice_scores = dice_metric_instance.aggregate()
        epoch_iou_scores = iou_metric_instance.aggregate()
        epoch_hd_scores = hd_metric_instance.aggregate()
        epoch_conf_matrix_results = confusion_matrix_metric_instance.aggregate()

        # 计算平均值
        mean_dice = torch.mean(epoch_dice_scores[~torch.isnan(epoch_dice_scores)]).item() if torch.is_tensor(epoch_dice_scores) else np.nanmean(epoch_dice_scores)
        mean_iou = torch.mean(epoch_iou_scores[~torch.isnan(epoch_iou_scores)]).item() if torch.is_tensor(epoch_iou_scores) else np.nanmean(epoch_iou_scores)
        mean_hd95 = torch.mean(epoch_hd_scores[~torch.isnan(epoch_hd_scores)]).item() if torch.is_tensor(epoch_hd_scores) else np.nanmean(epoch_hd_scores)

        # 输出整洁的指标信息
        print(f"\n📏 [Epoch {epoch+1} 评估指标]")
        print(f"   ├─ Mean Dice Score: {mean_dice:.4f}")
        print(f"   ├─ Mean IoU (MIoU): {mean_iou:.4f}")
        print(f"   └─ Mean HD95: {mean_hd95:.4f}")
        
        logger_instance.info(f"📏 Epoch {epoch+1} 指标 | Dice={mean_dice:.4f} | IoU={mean_iou:.4f} | HD95={mean_hd95:.4f}")

        # 记录到TensorBoard
        writer_instance.add_scalar('Metrics/Test_MeanDice', mean_dice, epoch)
        writer_instance.add_scalar('Metrics/Test_MIoU', mean_iou, epoch)
        writer_instance.add_scalar('Metrics/Test_MeanHD95', mean_hd95, epoch)

        # 逐类别指标记录（简化输出）
        logger_instance.debug(f"📏 记录 {len(classes_list)} 个类别的详细指标")
        for cls_idx, cls_name in enumerate(classes_list):
            if cls_idx < len(epoch_dice_scores):
                dice_val = epoch_dice_scores[cls_idx].item() if torch.is_tensor(epoch_dice_scores[cls_idx]) else epoch_dice_scores[cls_idx]
                writer_instance.add_scalar(f'Metrics_Class/Test_Dice_{cls_name}', dice_val, epoch)
            if cls_idx < len(epoch_iou_scores):
                iou_val = epoch_iou_scores[cls_idx].item() if torch.is_tensor(epoch_iou_scores[cls_idx]) else epoch_iou_scores[cls_idx]
                writer_instance.add_scalar(f'Metrics_Class/Test_IoU_{cls_name}', iou_val, epoch)
            if cls_idx < len(epoch_hd_scores):
                hd_val = epoch_hd_scores[cls_idx].item() if torch.is_tensor(epoch_hd_scores[cls_idx]) else epoch_hd_scores[cls_idx]
                writer_instance.add_scalar(f'Metrics_Class/Test_HD95_{cls_name}', hd_val, epoch)
        
        # 混淆矩阵衍生指标处理
        mpa_sum = 0
        num_valid_classes_for_mpa = 0
        
        for metric_idx, metric_name in enumerate(conf_matrix_metric_names_list):
            per_class_values = epoch_conf_matrix_results[:, metric_idx]
            mean_metric_value = torch.mean(per_class_values[~torch.isnan(per_class_values)]).item() if torch.is_tensor(per_class_values) else np.nanmean(per_class_values)
            
            print(f"   ├─ Mean {metric_name.capitalize()}: {mean_metric_value:.4f}")
            writer_instance.add_scalar(f'Metrics/Test_Mean_{metric_name.capitalize()}', mean_metric_value, epoch)
            
            for cls_idx, cls_name in enumerate(classes_list):
                if cls_idx < len(per_class_values):
                    metric_val = per_class_values[cls_idx].item() if torch.is_tensor(per_class_values[cls_idx]) else per_class_values[cls_idx]
                    writer_instance.add_scalar(f'Metrics_Class/Test_{metric_name.capitalize()}_{cls_name}', metric_val, epoch)
                    if metric_name == "accuracy" and not (torch.is_tensor(metric_val) and torch.isnan(metric_val) or np.isnan(metric_val)):
                        mpa_sum += metric_val
                        num_valid_classes_for_mpa += 1
        
        if num_valid_classes_for_mpa > 0:
            mean_pixel_accuracy = mpa_sum / num_valid_classes_for_mpa
            print(f"   └─ Mean Pixel Accuracy (MPA): {mean_pixel_accuracy:.4f}")
            writer_instance.add_scalar('Metrics/Test_MPA', mean_pixel_accuracy, epoch)
            logger_instance.debug(f"📏 MPA计算: {mpa_sum:.4f}/{num_valid_classes_for_mpa} = {mean_pixel_accuracy:.4f}")
        else:
            print(f"   └─ Mean Pixel Accuracy (MPA): N/A")
            writer_instance.add_scalar('Metrics/Test_MPA', float('nan'), epoch)

        logger_instance.debug(f"📏 Epoch {epoch+1}: 指标计算和记录完成")

    except Exception as e:
        logger_instance.error(f"❌ Epoch {epoch+1} 指标聚合错误: {e}")
        logger_instance.error(traceback.format_exc())
    finally:
        logger_instance.debug(f"📏 Epoch {epoch+1}: 重置指标状态")
        dice_metric_instance.reset()
        iou_metric_instance.reset()
        hd_metric_instance.reset()
        confusion_matrix_metric_instance.reset()

def reset_learning_rate_scheduler(optimizer, scheduler, initial_lr, initial_lr_wt_enhancer, num_epochs, current_epoch, use_wt_enhancer, logger_instance):
    """
    重置学习率调度器，实现周期性重启余弦退火
    """
    import torch.optim as optim
    
    # 重置学习率到初始值
    for param_group in optimizer.param_groups:
        if use_wt_enhancer and initial_lr_wt_enhancer and abs(param_group.get('lr', 0) - initial_lr_wt_enhancer) < 1e-10:  # WTEnhancer的参数组
            param_group['lr'] = initial_lr_wt_enhancer
        else:  # SAM mask decoder的参数组
            param_group['lr'] = initial_lr
    
    # 创建新的余弦退火调度器，从当前epoch开始到训练结束
    remaining_epochs = max(1, num_epochs - current_epoch)  # 确保至少有1个epoch
    new_scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, 
        T_max=remaining_epochs, 
        last_epoch=-1  # 重新开始
    )
    
    logger_instance.info(f"🔄 学习率重置: SAM={initial_lr}, WTEnhancer={initial_lr_wt_enhancer if use_wt_enhancer else 'N/A'}, 剩余epochs={remaining_epochs}")
    
    return new_scheduler

def generate_pseudo_masks_for_update(
    sam_model, 
    wt_enhancer, 
    image_path_or_tensor, 
    box_coords_by_class, 
    sam_transform, 
    device, 
    update_morph_kernel_size, 
    update_erosion_iterations, 
    update_dilation_iterations, 
    classes_list, 
    logger_instance
):
    """
    为单个图像生成并优化伪标签，用于伪标签更新流程。
    与`generate_initial_pseudo_labels.py`的逻辑保持一致。
    """
    try:
        # 1. 图像加载和预处理
        if isinstance(image_path_or_tensor, str):
            image_pil = Image.open(image_path_or_tensor).convert("RGB")
            image_np = np.array(image_pil)
        elif isinstance(image_path_or_tensor, torch.Tensor):
            image_np = image_path_or_tensor.permute(1, 2, 0).cpu().numpy() * 255.0
            image_np = image_np.astype(np.uint8)
        else:
            logger_instance.error(f"❌ 不支持的图像输入类型: {type(image_path_or_tensor)}")
            return None

        original_image_size = tuple(image_np.shape[:2])
        
        # SAM预处理
        input_image_resized_np = sam_transform.apply_image(image_np)
        input_image_resized_torch = torch.as_tensor(input_image_resized_np, device=device).permute(2, 0, 1).contiguous()
        input_image_final_torch = sam_model.preprocess(input_image_resized_torch.unsqueeze(0))

        with torch.no_grad():
            image_embedding = sam_model.image_encoder(input_image_final_torch)
            if wt_enhancer:
                image_embedding = wt_enhancer(image_embedding)
            
            final_masks_list = []

            for cls_name in classes_list:
                boxes_for_cls_original = box_coords_by_class.get(cls_name, torch.empty(0, 4))
                
                # 确保边界框是正确的形状 [N, 4]
                if boxes_for_cls_original.dim() == 1 and boxes_for_cls_original.shape[0] == 4:
                    boxes_for_cls_original = boxes_for_cls_original.unsqueeze(0)
                
                num_boxes_for_class = boxes_for_cls_original.shape[0]
                current_class_final_mask_np = np.zeros(original_image_size, dtype=np.uint8)

                if num_boxes_for_class > 0:
                    for box_idx in range(num_boxes_for_class):
                        current_box_original_coords = boxes_for_cls_original[box_idx, :].cpu().numpy()

                        # 🚀 修改：使用点提示模式替代边界框提示
                        # 计算边界框中心点
                        x1, y1, x2, y2 = current_box_original_coords
                        center_x = (x1 + x2) / 2.0
                        center_y = (y1 + y2) / 2.0
                        center_point = np.array([[center_x, center_y]])

                        # 应用坐标变换
                        point_transformed = sam_transform.apply_coords(center_point, original_image_size)
                        point_coords = torch.as_tensor(point_transformed, dtype=torch.float, device=device).unsqueeze(0)  # [1, 1, 2]
                        point_labels = torch.ones(1, 1, device=device, dtype=torch.int)  # [1, 1] 前景点

                        sparse_embeddings, dense_embeddings = sam_model.prompt_encoder(
                            points=(point_coords, point_labels),
                            boxes=None,
                            masks=None
                        )

                        low_res_masks, iou_predictions = sam_model.mask_decoder(
                            image_embeddings=image_embedding,
                            image_pe=sam_model.prompt_encoder.get_dense_pe(),
                            sparse_prompt_embeddings=sparse_embeddings,
                            dense_prompt_embeddings=dense_embeddings,
                            multimask_output=True, 
                        )
                        
                        best_mask_idx = torch.argmax(iou_predictions[0], dim=0)
                        selected_low_res_mask = low_res_masks[:, best_mask_idx:best_mask_idx+1, :, :]
                        
                        full_res_mask_torch = sam_model.postprocess_masks(
                            selected_low_res_mask,
                            input_size=tuple(input_image_resized_torch.shape[-2:]),
                            original_size=original_image_size
                        )

                        mask_np = (full_res_mask_torch[0, 0] > sam_model.mask_threshold).cpu().numpy().astype(np.uint8)
                        
                        if np.sum(mask_np) > 0:
                           current_class_final_mask_np = np.logical_or(current_class_final_mask_np, mask_np)

                # 形态学操作
                if np.sum(current_class_final_mask_np) > 0:
                    kernel = np.ones((update_morph_kernel_size, update_morph_kernel_size), np.uint8)
                    if update_erosion_iterations > 0:
                        current_class_final_mask_np = cv2.erode(current_class_final_mask_np.astype(np.uint8), kernel, iterations=update_erosion_iterations)
                    if update_dilation_iterations > 0:
                        current_class_final_mask_np = cv2.dilate(current_class_final_mask_np.astype(np.uint8), kernel, iterations=update_dilation_iterations)
                
                final_masks_list.append(current_class_final_mask_np.astype(np.uint8))

            if not final_masks_list or len(final_masks_list) != len(classes_list):
                logger_instance.error(f"❌ 生成的类别掩码数量不匹配")
                return None

            return np.stack(final_masks_list, axis=0)

    except Exception as e:
        logger_instance.error(f"❌ 伪标签生成失败: {e}")
        import traceback
        logger_instance.error(traceback.format_exc())
        return None

# ==================== 聚合数据集批处理collate函数 ====================
def aggregated_collate_fn(batch):
    """
    聚合数据集的整理函数，处理预计算嵌入数据
    实现方案6和7：聚合数据集加载器
    
    Args:
        batch: 来自AggregatedPerBoxDataset的数据列表
    
    Returns:
        dict: 包含数据列表的字典，支持批量处理
    """
    embeddings = []
    masks = []
    fidt_maps = []  # 新增：FIDT图列表
    bboxes = []
    class_names = []
    class_ids = []
    box_ids = []

    for item in batch:
        embeddings.append(item['embedding'])
        masks.append(item['mask'])
        fidt_maps.append(item['fidt_map'])  # 新增：收集FIDT图
        bboxes.append(item['bbox'])
        class_names.append(item['class_name'])
        class_ids.append(item['class_id'])
        box_ids.append(item['box_id'])

    # 对于可以堆叠的数据进行堆叠
    try:
        bboxes_stacked = torch.stack(bboxes, dim=0)
        class_ids_stacked = torch.stack(class_ids, dim=0) if isinstance(class_ids[0], torch.Tensor) else torch.tensor(class_ids)
        box_ids_stacked = torch.stack(box_ids, dim=0) if isinstance(box_ids[0], torch.Tensor) else torch.tensor(box_ids)
    except:
        bboxes_stacked = bboxes
        class_ids_stacked = class_ids
        box_ids_stacked = box_ids

    return {
        'embeddings': embeddings,    # 预计算的图像嵌入列表
        'masks': masks,              # 掩码列表
        'fidt_maps': fidt_maps,      # FIDT图列表 新增
        'bboxes': bboxes_stacked,
        'class_names': class_names,
        'class_ids': class_ids_stacked,
        'box_ids': box_ids_stacked
    }

def parse_args():
    parser = argparse.ArgumentParser(description='训练SAM模型')
    parser.add_argument('--data_root', type=str, default='/datadisk/SAM/processed_larch_dataset',
                        help='处理后的数据集根目录')
    parser.add_argument('--sam_checkpoint', type=str, default='/datadisk/SAM/sam_vit_h_4b8939.pth',
                        help='SAM模型检查点路径')
    parser.add_argument('--model_type', type=str, default='vit_h',
                        help='SAM模型类型')
    parser.add_argument('--output_dir', type=str, default='/datadisk/SAM/output',
                        help='输出目录')
    parser.add_argument('--batch_size', type=int, default=1,
                        help='批次大小 (建议为1)')
    parser.add_argument('--num_epochs', type=int, default=30,
                        help='训练轮数')
    parser.add_argument('--lr', type=float, default=1e-5,
                        help='学习率')
    parser.add_argument('--weight_decay', type=float, default=0.1,
                        help='权重衰减')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    
    # 伪标签更新相关参数
    parser.add_argument('--update_interval', type=int, default=5,
                        help='伪标签更新间隔 (epoch数), >0 时启用更新')
    parser.add_argument('--initial_pseudo_label_update_epoch', type=int, default=10,
                        help='首次允许进行伪标签更新的epoch数 (从0开始计数)')
    parser.add_argument('--update_morph_kernel_size', type=int, default=3,
                        help='伪标签更新时形态学操作的核大小')
    parser.add_argument('--update_erosion_iterations', type=int, default=10, 
                        help='伪标签更新时腐蚀操作的迭代次数')
    parser.add_argument('--update_dilation_iterations', type=int, default=10, 
                        help='伪标签更新时膨胀操作的迭代次数')

    parser.add_argument('--loss_type', type=str, default='combined',
                        choices=['dice_focal', 'auc', 'combined'],
                        help='损失函数类型: dice_focal(原始), auc(仅AUC), combined(联合)')
    parser.add_argument('--weight_method', type=str, default='inverse',
                        choices=['none', 'inverse', 'effective'],
                        help='类别权重计算方法')
    parser.add_argument('--auc_loss_name', type=str, default='log_loss', 
                        choices=['log_loss', 'max_loss', 'square_loss'], 
                        help='AUC损失的具体计算方式 (log_loss, max_loss, square_loss)')
    parser.add_argument('--auc_epsilon', type=float, default=0.05, 
                        help='AUC损失中的epsilon参数')
    parser.add_argument('--auc_samples', type=int, default=1000, 
                        help='AUC损失计算时每个类别采样的最大正负对数')
    parser.add_argument('--accumulation_steps', type=int, default=8, help='梯度累积步数，增加以提升GPU利用率')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪的最大范数值')
    parser.add_argument('--use_amp', action='store_true', help='是否使用混合精度训练')
    parser.add_argument('--use_wt_enhancer', action='store_true', help='是否启用小波增强模块处理图像嵌入')
    parser.add_argument('--wt_enhancer_levels', type=int, default=2, help='小波增强模块的分解级别数')
    parser.add_argument('--wt_enhancer_wavelet', type=str, default='bior2.2', help='小波增强模块使用的小波基')
    parser.add_argument('--wt_enhancer_kernel_size', type=int, default=3, help='小波增强模块内部卷积核大小')
    parser.add_argument('--lr_wt_enhancer', type=float, default=1e-4, help='小波增强模块的学习率')
    # 不确定性权重学习率（新）
    parser.add_argument('--lr_uncertainty', type=float, default=1e-2, help='不确定性权重参数的学习率')



    # 添加调试选项
    parser.add_argument('--debug', action='store_true',
                        help='调试模式，限制样本数量以加快速度')
    
    # 优化DataLoader配置
    parser.add_argument('--num_workers', type=int, default=8,
                        help='数据加载器的工作进程数')

    parser.add_argument('--eval_mode', action='store_true', help='启用评估模式 (将 is_training_mode 设为 False)')

    return parser.parse_args()

def optimize_cpu_usage(num_workers):
    """
    根据DataLoader worker数量优化CPU使用
    避免PyTorch线程和DataLoader worker之间的资源竞争
    """
    # 计算最优的PyTorch线程数
    # 总CPU核心数 - DataLoader workers - 系统保留核心
    available_cores = 128
    system_reserved = 4  # 为系统进程保留核心
    optimal_pytorch_threads = max(
        1,  # 至少1个线程
        min(
            64,  # 最大64个线程（PyTorch默认）
            available_cores - num_workers - system_reserved
        )
    )
    
    # 设置PyTorch线程数
    torch.set_num_threads(optimal_pytorch_threads)
    
    # 设置OpenMP线程数（如果使用MKL-DNN）
    import os
    os.environ['OMP_NUM_THREADS'] = str(optimal_pytorch_threads)
    os.environ['MKL_NUM_THREADS'] = str(optimal_pytorch_threads)
    
    print(f"🔧 CPU优化配置:")
    print(f"  - 总CPU核心: {available_cores}")
    print(f"  - DataLoader workers: {num_workers}")
    print(f"  - PyTorch线程: {optimal_pytorch_threads}")
    print(f"  - 系统保留核心: {system_reserved}")
    
    return optimal_pytorch_threads

def train_with_per_box_metrics(args):

    args.is_training_mode = not args.eval_mode

    """
    使用按框计算指标的增强训练函数
    
    ✅ 完成的训练速度提升优化：
    1. 指标系统增强：
       - 修复HD95计算错误，增加空掩码处理和调试信息
       - 新增混淆矩阵衍生指标：Accuracy、Precision、Recall、F1-Score、Specificity
       - 新增边界相关指标：Surface Dice、Boundary IoU
       - 重构指标聚合系统，支持总体和按类别指标计算
       - 更新TensorBoard记录，使用Metrics/Overall/和Metrics/Per_Class/层级结构
       - 增强日志输出，详细显示所有指标的训练和测试结果
    
    2. 移除多卡训练代码：
       - 移除所有分布式训练（DDP）相关代码
       - 简化设备选择逻辑为单GPU模式
       - 清理所有is_main_process条件判断
    
    3. 实现多框批处理：
       - 创建aggregated_collate_fn函数处理聚合数据集批处理
       - 重构训练循环，支持批次内多框处理
       - 实现梯度累积效果
    
    4. 启用TF32性能优化：
       - 自动检测GPU架构并启用TF32
       - 同时启用matmul和cudnn的TF32支持
    
    5. 优化DataLoader配置：
       - 将num_workers从2提升到8
       - 启用pin_memory=True加速数据传输
       - 添加可配置的num_workers参数
    
    6. 核心改造 - 预计算嵌入模式：
       - 只使用聚合数据集（预计算嵌入）
       - 移除原始数据集模式，跳过图像编码器计算
       - 直接使用预计算嵌入进行训练，大幅提升训练速度
    """
    # 启用TF32以提升性能（如果支持）
    if torch.cuda.is_available() and torch.cuda.get_device_capability()[0] >= 8:
        print("✅ 启用TF32以提升Ampere及更新架构GPU的性能")
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
    
    # 预优化CPU使用（基于估算的worker数量）
    estimated_workers = min(args.num_workers, 16)
    pytorch_threads = optimize_cpu_usage(estimated_workers)
    
    # 系统依赖检查
    check_system_dependencies()
    
    # 简化设备选择
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 基础设置
    os.makedirs(args.output_dir, exist_ok=True)
    logger = SimpleLogger(os.path.join(args.output_dir, 'training.log'))
    logger.log(f"开始按框指标训练，参数: {vars(args)}")
    logger.log(f"设备: {device}")
    

    
    # 初始GPU状态
    gpu_info = get_safe_gpu_info()
    logger.log(f"初始GPU状态: {gpu_info}")
    
    # 导入聚合数据集类
    from torch.utils.data import DataLoader
    
    # 只使用聚合数据集（方案7：嵌入预计算与数据聚合）
    aggregated_data_path = os.path.join(args.data_root, 'aggregated_dataset')
    
    try:
        from larch_dataset import AggregatedPerBoxDataset
        train_dataset = AggregatedPerBoxDataset(args.data_root, 'train')
        test_dataset = AggregatedPerBoxDataset(args.data_root, 'test')
        logger.log(f"✅ 使用聚合数据集（预计算嵌入模式）")
        logger.log(f"训练数据集框数量: {len(train_dataset)}")
        logger.log(f"测试数据集框数量: {len(test_dataset)}")
    except (ImportError, FileNotFoundError) as e:
        logger.log(f"❌ 聚合数据集加载失败: {e}")
        logger.log("请先运行generate_initial_pseudo_labels.py生成聚合数据集")
        logger.log("命令: python generate_initial_pseudo_labels.py --split train")
        logger.log("命令: python generate_initial_pseudo_labels.py --split test")
        return
    
    # 使用聚合数据集的collate函数
    collate_fn = aggregated_collate_fn
    
    # 创建数据加载器（基于128核CPU优化配置）
    # 根据CPU性能和内存动态调整参数
    effective_batch_size = max(args.batch_size, 16)  # 适中的batch size，避免内存压力
    
    # 🚀 DataLoader内存优化配置（避免OOM）
    # 大幅减少内存使用，防止系统杀死进程
    optimal_workers = min(
        args.num_workers,           # 用户指定的worker数
        4,                         # 最大4个worker（避免内存爆炸）
        len(train_dataset) // 200   # 动态调整：每200个样本1个worker
    )

    # 高性能DataLoader配置
    dataloader_config = {
        'batch_size': effective_batch_size,
        'num_workers': optimal_workers,
        'pin_memory': True,                    # 启用锁页内存加速GPU传输
        'collate_fn': collate_fn,
        'persistent_workers': True,            # 保持worker进程，减少启动开销
        'timeout': 60,                        # 增加超时时间到5分钟
        'multiprocessing_context': 'spawn'    # 使用spawn更稳定，避免fork导致的CUDA句柄问题
    }
    
    train_loader = DataLoader(
        train_dataset,
        shuffle=True,
        drop_last=True,
        **dataloader_config
    )
    test_loader = DataLoader(
        test_dataset, 
        shuffle=False,
        drop_last=False,
        **dataloader_config
    )
    
    # 📊 DataLoader性能优化报告
    logger.log(f"📊 数据加载器优化配置:")
    logger.log(f"  - 系统CPU核心: 128核 (物理64核 × 2线程)")
    logger.log(f"  - 系统内存: 112GB总内存，110GB可用")
    logger.log(f"  - 数据集大小: 训练{len(train_dataset)}框，测试{len(test_dataset)}框")
    logger.log(f"  - 优化后批次大小: {effective_batch_size} (原始: {args.batch_size})")
    logger.log(f"  - 优化后worker数: {optimal_workers} (原始: {args.num_workers})")
    logger.log(f"  - 超时设置: 60秒 ")
    logger.log(f"  - 多进程上下文: spawn (Linux优化)")
    logger.log(f"🔧 使用多框批处理: {'是' if effective_batch_size > 1 else '否'}")
    logger.log(f"⚡ 预计算嵌入模式: 跳过图像编码器计算")
    
    # 预估性能提升
    theoretical_speedup = min(optimal_workers / max(args.num_workers, 1), 4.0)  # 最大4倍加速
    logger.log(f"📈 预估DataLoader性能提升: {theoretical_speedup:.1f}x")
    
    # 🔍 DataLoader健康检查
    def test_dataloader_performance(loader, name, test_batches=3):
        """测试DataLoader性能，确保配置正确"""
        logger.log(f"🔍 测试{name}DataLoader性能...")
        try:
            start_time = time.time()
            loader_iter = iter(loader)
            
            for i in range(min(test_batches, len(loader))):
                batch_start = time.time()
                data = next(loader_iter)
                batch_time = time.time() - batch_start
                
                # 获取批次信息
                if isinstance(data, dict) and 'embeddings' in data:
                    batch_size = len(data['embeddings'])
                    logger.log(f"  批次{i+1}: {batch_size}个框, 加载耗时{batch_time:.3f}s")
                
                # 如果第一个批次加载时间过长，发出警告
                if i == 0 and batch_time > 30:
                    logger.log(f"  ⚠️ 首批次加载较慢({batch_time:.1f}s)，这是正常的worker启动开销")
                elif batch_time > 10:
                    logger.log(f"  ⚠️ 批次加载较慢({batch_time:.1f}s)，可能需要调整配置")
            
            total_time = time.time() - start_time
            avg_time = total_time / test_batches
            logger.log(f"  ✅ {name}DataLoader测试完成: 平均每批次{avg_time:.3f}s")
            return True
            
        except Exception as e:
            logger.log(f"  ❌ {name}DataLoader测试失败: {e}")
            return False
    
    # 根据实际worker数量重新优化CPU使用
    final_pytorch_threads = optimize_cpu_usage(optimal_workers)
    logger.log(f"🔧 最终CPU配置: PyTorch线程={final_pytorch_threads}, DataLoader workers={optimal_workers}")
    
    # 执行DataLoader健康检查
    if test_dataloader_performance(train_loader, "训练", test_batches=2):
        logger.log(f"✅ DataLoader配置验证成功")
    else:
        logger.log(f"⚠️ DataLoader可能存在问题，但继续训练")
    
    # 获取类别信息（在模型加载前）
    with open(os.path.join(args.data_root, 'dataset_info.json'), 'r') as f:
            dataset_info = json.load(f)
            CLASSES = dataset_info['classes']
            NUM_CLASSES = len(CLASSES)
    
    # 加载SAM模型（支持小波增强）
    wt_enhancer = None
    checkpoint = None
    
    try:
        logger.log(f"加载SAM模型: {args.model_type} from {args.sam_checkpoint}")
        
        checkpoint = torch.load(args.sam_checkpoint, map_location=device)
        sam = sam_model_registry[args.model_type](checkpoint=None)
        
        # 检查点加载逻辑
        sam_state_dict_key = None
        if 'sam_state_dict' in checkpoint:
            sam_state_dict_key = 'sam_state_dict'
        elif 'model_state_dict' in checkpoint:
            sam_state_dict_key = 'model_state_dict'
        elif 'model' in checkpoint:
            sam_state_dict_key = 'model'

        if sam_state_dict_key:
            sam.load_state_dict(checkpoint[sam_state_dict_key], strict=False)
        else:
            sam.load_state_dict(checkpoint, strict=False) 
            logger.log("警告: 直接加载检查点作为状态字典")
        
        sam.to(device)
        logger.log(f"SAM模型 ({args.model_type}) 加载成功")

        # WTEnhancer处理
        if args.use_wt_enhancer:
            if sam and hasattr(sam, 'prompt_encoder'):
                image_embedding_dim = sam.prompt_encoder.embed_dim
                 
            if 'wt_enhancer_state_dict' in checkpoint and 'wt_enhancer_args' in checkpoint:
                enhancer_args_loaded = checkpoint['wt_enhancer_args']
                wt_enhancer_levels = enhancer_args_loaded.get('levels', args.wt_enhancer_levels)
                wt_enhancer_wavelet = enhancer_args_loaded.get('wavelet', args.wt_enhancer_wavelet)
                wt_enhancer_kernel_size = enhancer_args_loaded.get('kernel_size', args.wt_enhancer_kernel_size)
        
                wt_enhancer = WTEnhancedBlock(
                    in_channels=image_embedding_dim,
                    out_channels=image_embedding_dim,
                    wt_levels=wt_enhancer_levels,
                    wavelet_type=wt_enhancer_wavelet,
                    kernel_size=wt_enhancer_kernel_size,
                ).to(device)
                wt_enhancer.load_state_dict(checkpoint['wt_enhancer_state_dict'])
                logger.log(f"WTEnhancer从检查点加载 (级别: {wt_enhancer.wt_levels}, 小波: {wt_enhancer.wavelet_type})")
            else:
                logger.log("检查点中未找到预训练的WTEnhancer，将初始化新的")
                wt_enhancer = WTEnhancedBlock(
                    in_channels=image_embedding_dim,
                    out_channels=image_embedding_dim,
                    wt_levels=args.wt_enhancer_levels,
                    wavelet_type=args.wt_enhancer_wavelet,
                    kernel_size=args.wt_enhancer_kernel_size,
                ).to(device)
                logger.log("新的WTEnhancer已初始化")
        else:
            logger.log("WTEnhancer未启用")
                
    except FileNotFoundError:
        logger.log(f"错误: SAM检查点 {args.sam_checkpoint} 未找到")
        return
    except Exception as e:
        logger.log(f"加载模型时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 参数冻结
    logger.log("冻结SAM Image Encoder和Prompt Encoder参数...")
    for param in sam.image_encoder.parameters():
        param.requires_grad = False
    for param in sam.prompt_encoder.parameters():
        param.requires_grad = False
    
    # 直接使用模型
    sam.train()
    
    # 🚀 创建STE模块用于可学习阈值
    ste_module = LearnableThresholdModule(
        num_classes=len(CLASSES),
        init_threshold=0.5,
        learn_threshold=True
    ).to(device)
    logger.log(f"STE模块已创建，支持{len(CLASSES)}个类别的可学习阈值")
    
    # 设置优化器（支持小波增强双学习率、不确定性权重和STE阈值）
    optimizer_params = [{'params': sam.mask_decoder.parameters(), 'lr': args.lr}]
    
    if args.use_wt_enhancer and wt_enhancer:
        # ★ 修改: 为小波增强器的门控和alpha参数设置特殊学习率（10倍主干）
        learnable_params = wt_enhancer.get_learnable_parameters()
        
        # 创建特殊参数的id集合，用于快速查找
        special_param_ids = set()
        for param in learnable_params['alpha']:
            special_param_ids.add(id(param))
        for param in learnable_params['gates']:
            special_param_ids.add(id(param))
        
        # 常规参数：使用标准学习率
        regular_params = []
        for param in wt_enhancer.parameters():
            if id(param) not in special_param_ids:
                regular_params.append(param)
        
        if regular_params:
            optimizer_params.append({'params': regular_params, 'lr': args.lr_wt_enhancer})
            logger.log(f"WTEnhancer常规参数已添加到优化器，学习率={args.lr_wt_enhancer}")
        
        # 门控和alpha参数：使用10倍学习率
        special_lr = args.lr_wt_enhancer * 10
        if learnable_params['alpha']:
            optimizer_params.append({'params': learnable_params['alpha'], 'lr': special_lr})
            logger.log(f"WTEnhancer Alpha参数已添加到优化器，学习率={special_lr} (10倍)")
        
        if learnable_params['gates']:
            optimizer_params.append({'params': learnable_params['gates'], 'lr': special_lr})
            logger.log(f"WTEnhancer 门控参数已添加到优化器，学习率={special_lr} (10倍)")
        
        # 设置class-aware scaling
        if hasattr(wt_enhancer, 'set_class_aware_scale'):
            # 计算前景稀疏类的放大因子
            total_pixels = 1500 * 1500
            target_pixels = total_pixels * 0.1  # 假设前景占10%
            scale_factor = math.log(total_pixels / target_pixels)
            wt_enhancer.set_class_aware_scale(scale_factor)
            logger.log(f"WTEnhancer设置class-aware scaling因子: {scale_factor:.2f}")
        
        for param in wt_enhancer.parameters():
            param.requires_grad = True
    
    # 添加STE阈值参数到优化器
    ste_params = list(ste_module.parameters())
    if ste_params:
        optimizer_params.append({'params': ste_params, 'lr': args.lr * 0.1})  # 使用较小的学习率
        logger.log(f"STE阈值参数已添加到优化器，学习率={args.lr * 0.1}")
        for i, threshold in enumerate(ste_module.get_thresholds()):
            logger.log(f"  初始阈值[{i}]: {threshold:.4f}")

    optimizer = torch.optim.AdamW(optimizer_params, weight_decay=args.weight_decay)
    
    # 学习率调度器（修复PyTorch 1.1.0+警告）
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs, last_epoch=-1)
    
    # 从检查点恢复优化器和调度器状态
    start_epoch = 0
    if checkpoint and 'optimizer_state_dict' in checkpoint and 'epoch' in checkpoint:
        try:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint['epoch']
            logger.log(f"优化器状态已从检查点恢复，将从epoch {start_epoch + 1}继续")
        except Exception as e:
            logger.log(f"警告: 无法从检查点恢复优化器状态: {e}，将使用新初始化的优化器")
            start_epoch = 0

    if checkpoint and 'scheduler_state_dict' in checkpoint and start_epoch > 0:
        try:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            logger.log("调度器状态已从检查点恢复")
        except Exception as e:
            logger.log(f"警告: 无法从检查点恢复调度器状态: {e}，将使用新初始化的调度器")
            # 修复：避免在初始化时自动调用step()，总是使用last_epoch=-1
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs, last_epoch=-1)
            # ★ 修复: 不再手动设置last_epoch，避免触发自动step()
            if start_epoch > 0:
                import math
                # 只手动更新学习率到正确的epoch状态，不修改scheduler的内部状态
                for param_group in optimizer.param_groups:
                    # 计算当前epoch应该的学习率
                    base_lr = param_group.get('initial_lr', param_group['lr'])
                    cosine_lr = base_lr * 0.5 * (1 + math.cos(math.pi * start_epoch / args.num_epochs))
                    param_group['lr'] = cosine_lr
                logger.log(f"手动设置学习率到epoch {start_epoch}对应的值，scheduler将从epoch 0开始计数")
    
    # 计算类别权重（使用框级别权重计算）
    class_weights = None
    if args.weight_method != 'none':
        try:
            # 对于单框训练，使用新的框级别权重计算
            class_weights = calculate_box_level_class_weights(train_dataset, CLASSES, method=args.weight_method)
            if class_weights is not None:
                class_weights = class_weights.to(device)
                logger.log(f"类别权重计算完成 ({args.weight_method}): {class_weights}")
        except Exception as e:
            logger.log(f"类别权重计算失败: {e}")
            import traceback
            traceback.print_exc()
            class_weights = None
    
    # 初始化损失函数（支持多种类型）
    # 对于单框训练，创建损失聚合器
    loss_aggregator = None
    dice_focal_criterion = None
    
    if args.loss_type == 'dice_focal':
        # 使用真正的DiceFocal损失（通过MONAI实现）
        try:
            from monai.losses.dice import DiceFocalLoss
            dice_focal_criterion = DiceFocalLoss(
                to_onehot_y=False,
                softmax=False,
                sigmoid=True,
                squared_pred=True,
                jaccard=False,
                reduction='mean'
            )
            logger.log("使用MONAI DiceFocal损失")
        except ImportError as e:
            logger.log(f"MONAI DiceFocalLoss不可用: {e}，回退到BCE损失")
            dice_focal_criterion = nn.BCEWithLogitsLoss()
        except Exception as e:
            logger.log(f"MONAI DiceFocal初始化失败: {e}，回退到BCE损失")
            dice_focal_criterion = nn.BCEWithLogitsLoss()
        
        criterion = dice_focal_criterion
        
    elif args.loss_type == 'auc':
        # 使用新的单框二分类AUC损失
        criterion = PerBoxBinaryAUCLoss(
            epsilon=args.auc_epsilon,
            loss_type=args.auc_loss_name,
            max_samples_per_box=args.auc_samples,
            sampling_strategy='random',
            class_weights=class_weights,
            debug=args.debug
        )
        # 创建类别感知的损失聚合器
        loss_aggregator = ClassAwareBoxAggregator(
            classes_list=CLASSES,
            class_weights=class_weights,
            aggregation_method='weighted_mean'
        )
        logger.log(f"使用单框二分类AUC损失 (epsilon={args.auc_epsilon}, max_samples={args.auc_samples})")
        
    elif args.loss_type == 'combined':
        # 联合损失：DiceFocal + AUC
        # 1. DiceFocal损失
        try:
            from monai.losses.dice import DiceFocalLoss
            dice_focal_criterion = DiceFocalLoss(
                to_onehot_y=False,
                softmax=False,
                sigmoid=True,
                squared_pred=True,
                jaccard=False,
                reduction='mean'
            )
            logger.log("联合损失：使用MONAI DiceFocal")
        except ImportError as e:
            dice_focal_criterion = nn.BCEWithLogitsLoss()
            logger.log(f"MONAI DiceFocalLoss不可用: {e}，使用BCE")
        except Exception as e:
            dice_focal_criterion = nn.BCEWithLogitsLoss()
            logger.log(f"DiceFocal初始化失败，使用BCE: {e}")
        
        # 2. AUC损失
        try:
            auc_criterion = PerBoxBinaryAUCLoss(
                epsilon=args.auc_epsilon,
                loss_type=args.auc_loss_name,
                max_samples_per_box=args.auc_samples,
                sampling_strategy='random',
                class_weights=class_weights,
                debug=args.debug
            )
        except Exception as e:
            logger.log(f"AUC损失初始化失败: {e}，回退到BCE损失")
            auc_criterion = nn.BCEWithLogitsLoss()
        
        # 🚀 基于不确定性的联合损失（移除阶段化训练）
        class CombinedLoss(nn.Module):
            def __init__(self, dice_focal_loss, auc_loss):
                super().__init__()
                self.dice_focal_loss = dice_focal_loss
                self.auc_loss = auc_loss
                
                # 基于不确定性的自动加权器（2个损失：DiceFocal + AUC）
                self.uncertainty_weighter = AutomaticWeightedLoss(num_losses=2)
                
            def forward(self, pred_logits_4d, pred_probs, target_mask_4d, class_id=None, box_id=None):
                # DiceFocal损失（使用4D张量）
                dice_focal_loss_raw = self.dice_focal_loss(pred_logits_4d, target_mask_4d)
                
                # AUC损失（使用2D概率和目标）
                target_mask_2d = target_mask_4d.squeeze()  # [1,1,H,W] -> [H,W]
                
                # 注意：这里的实现假设 pred_probs 和 target_mask_2d 都是批处理的
                # auc_loss 现在返回一个 [B] 形状的张量
                auc_loss_raw_batch = self.auc_loss(pred_probs, target_mask_2d, class_id, box_id)
                auc_loss_raw = auc_loss_raw_batch.mean() # 对批内损失求平均

                # 使用对数归一化替代k归一化
                dice_focal_norm = torch.log(1.0 + dice_focal_loss_raw)
                auc_norm = torch.log(1.0 + auc_loss_raw)

                # 不确定性加权主要损失
                combined_loss = self.uncertainty_weighter(dice_focal_norm, auc_norm)
                
                # ★ 新增: HF重建损失
                hf_loss = torch.tensor(0.0, device=pred_logits_4d.device)
                if args.use_wt_enhancer and wt_enhancer and hasattr(wt_enhancer, 'get_hf_reconstruction_loss'):
                    hf_loss = wt_enhancer.get_hf_reconstruction_loss(weight=0.1)
                
                # 总损失 = 主要损失 + HF重建损失
                total_loss = combined_loss + hf_loss
                
                # 返回所有中间值用于日志记录
                return total_loss, dice_focal_loss_raw, auc_loss_raw_batch, dice_focal_norm, auc_norm, hf_loss
                
            def get_loss_weights_info(self):
                """获取不确定性权重的详细信息"""
                return self.uncertainty_weighter.get_loss_weights_info()
        
        criterion = CombinedLoss(
            dice_focal_criterion,
            auc_criterion
        )
        
        # 记录初始权重
        if hasattr(criterion, 'get_loss_weights_info'):
            initial_weights = criterion.get_loss_weights_info()
            logger.log(f"⚖️  初始不确定性权重: DiceFocal={initial_weights['dice_focal_weight']:.4f}, AUC={initial_weights['auc_weight']:.4f}")
        
        # 🔧 修复：正确添加不确定性权重参数到优化器
        logger.log(f"🔧 添加不确定性权重参数到优化器:")
        logger.log(f"  - criterion类型: {type(criterion)}")
        logger.log(f"  - uncertainty_weighter类型: {type(criterion.uncertainty_weighter)}")
        logger.log(f"  - log_vars参数: {list(criterion.uncertainty_weighter.parameters())}")
        
        # 确保不确定性参数被正确添加
        uncertainty_params = list(criterion.uncertainty_weighter.parameters())
        if uncertainty_params:
            # 使用更高的学习率以加速不确定性参数收敛
            optimizer_params.append({'params': uncertainty_params, 'lr': args.lr_uncertainty})
            logger.log(f"✅ 成功添加 {len(uncertainty_params)} 个不确定性参数到优化器")
            # 输出初始参数值 - 修复张量访问错误
            for i, param in enumerate(uncertainty_params):
                if param.numel() == 1:
                    logger.log(f"  - 初始log_var[{i}]: {param.data.item():.6f}")
                else:
                    # 多元素张量，逐个输出
                    for j in range(param.numel()):
                        logger.log(f"  - 初始log_var[{i}][{j}]: {param.data.flatten()[j].item():.6f}")
        else:
            logger.log(f"❌ 警告: 未找到不确定性参数!")
        
        # 创建类别感知的损失聚合器（用于AUC部分）
        loss_aggregator = ClassAwareBoxAggregator(
            classes_list=CLASSES,
            class_weights=class_weights,
            aggregation_method='weighted_mean'
        )
        
        logger.log(f"🎯 使用基于不确定性的联合损失:")
        logger.log(f"  - 自动学习DiceFocal和AUC损失的权重")
        logger.log(f"  - 使用对数归一化: log(1+loss)替代指数k归一化")
        logger.log(f"  - 基于任务不确定性进行加权")
        logger.log(f"  - AUC epsilon: {args.auc_epsilon}")
        
        # 重新创建优化器以包含不确定性参数
        optimizer = torch.optim.AdamW(optimizer_params, weight_decay=args.weight_decay)
        
        # 🔧 验证优化器是否包含不确定性参数
        logger.log(f"🔧 优化器验证:")
        for group_idx, param_group in enumerate(optimizer.param_groups):
            logger.log(f"  - 参数组 {group_idx}: {len(param_group['params'])} 个参数, lr={param_group['lr']}")
            if hasattr(param_group['params'][0], 'shape'):
                for param_idx, param in enumerate(param_group['params']):
                    logger.log(f"    参数[{param_idx}]: 形状={param.shape}, requires_grad={param.requires_grad}")
        
    else:
        # 默认使用单框AUC损失
        criterion = PerBoxBinaryAUCLoss(
            epsilon=0.05,
            loss_type='log_loss',
            max_samples_per_box=1500,
            sampling_strategy='random',
            class_weights=class_weights,
            debug=args.debug
        )
        loss_aggregator = ClassAwareBoxAggregator(
            classes_list=CLASSES,
            class_weights=class_weights,
            aggregation_method='weighted_mean'
        )
        logger.log("使用默认单框AUC损失")
    
    # TensorBoard
    tensorboard_log_dir = os.path.join(args.output_dir, 'logs')
    os.makedirs(tensorboard_log_dir, exist_ok=True)
    writer = SummaryWriter(tensorboard_log_dir)
    logger.log(f"TensorBoard日志保存到: {tensorboard_log_dir}")
    
    # 混合精度训练支持
    scaler = torch.cuda.amp.GradScaler() if args.use_amp else None
    if args.use_amp:
        logger.log("启用混合精度训练")
    
    # SAM变换
    sam_model_for_transform = sam
    
    # 安全获取img_size
    try:
        # 安全获取图像尺寸
        if hasattr(sam_model_for_transform, 'image_encoder') and hasattr(sam_model_for_transform.image_encoder, 'img_size'):
            img_size = sam_model_for_transform.image_encoder.img_size
            if isinstance(img_size, torch.Tensor):
                img_size = img_size.item()
            elif isinstance(img_size, (list, tuple)):
                img_size = img_size[0]
            else:
                img_size = int(img_size)  # 确保是整数
        else:
            img_size = 1024  # 默认值
        sam_transform = ResizeLongestSide(int(img_size))
        logger.log(f"SAM变换尺寸: {img_size}")
    except Exception as e:
        # 默认使用1024（SAM的标准尺寸）
        sam_transform = ResizeLongestSide(1024)
        logger.log(f"获取SAM尺寸失败: {e}，使用默认尺寸: 1024")
    
    # 设置MONAI指标（集成安全包装器）
    try:
        from monai.metrics.meandice import DiceMetric
        from monai.metrics.hausdorff_distance import HausdorffDistanceMetric
        from monai.metrics.confusion_matrix import ConfusionMatrixMetric
        
        dice_metric = DiceMetric(include_background=True, reduction="mean", get_not_nans=False)
        iou_metric = IoUMetric(include_background=True, reduction="mean", get_not_nans=False)
        hd_metric = SafeHausdorffDistanceMetric(include_background=True, reduction="mean", get_not_nans=False, percentile=95)
        
        # 混淆矩阵指标（多个）
        confusion_matrix_metrics = {
            'precision': SafeConfusionMatrixMetric(include_background=True, metric_name='precision', reduction="mean", get_not_nans=False),
            'recall': SafeConfusionMatrixMetric(include_background=True, metric_name='recall', reduction="mean", get_not_nans=False),
            'specificity': SafeConfusionMatrixMetric(include_background=True, metric_name='specificity', reduction="mean", get_not_nans=False),
            'f1': SafeConfusionMatrixMetric(include_background=True, metric_name='f1 score', reduction="mean", get_not_nans=False)
        }
        
        logger.log("MONAI指标系统初始化成功")
        MONAI_AVAILABLE = True
    except ImportError as e:
        logger.log(f"MONAI不可用，将使用自定义指标: {e}")
        dice_metric = None
        iou_metric = IoUMetric(include_background=True, reduction="mean", get_not_nans=False)
        hd_metric = None
        confusion_matrix_metrics = {}
        MONAI_AVAILABLE = False
    
    logger.log("开始按框训练...")
    
    best_test_loss = float('inf')
    best_test_dice = 0.0

    for epoch in range(start_epoch, args.num_epochs):
        
        # 训练阶段
        sam.train()
        if args.use_wt_enhancer and wt_enhancer:
            wt_enhancer.train()

        # 设置训练模式标志（用于动态点提示采样）
        args.is_training_mode = True

        train_per_box_metrics = []
        train_losses = []
        
        # 显示进度条
        train_pbar = tqdm(train_loader, desc=f"训练 Epoch {epoch+1}/{args.num_epochs}")
        
        # 优化梯度累积步数，充分利用GPU
        effective_accumulation_steps = max(args.accumulation_steps, 1)  # 至少4步累积
        
        for batch_idx, data in enumerate(train_pbar):
            try:
                # 🔍 每个batch的详细调试信息
                if batch_idx % 5 == 0:  # 每5个batch打印一次详细信息
                    print(f"\n🔍 训练Batch {batch_idx} 详细调试:")
                    print(f"  📊 Batch数据结构:")
                    for key, value in data.items():
                        if isinstance(value, list):
                            print(f"    - {key}: 列表长度={len(value)}, 元素类型={type(value[0]) if value else 'N/A'}")
                            if value and hasattr(value[0], 'shape'):
                                print(f"      首个元素形状: {value[0].shape}")
                        elif hasattr(value, 'shape'):
                            print(f"    - {key}: 张量形状={value.shape}")
                        else:
                            print(f"    - {key}: 类型={type(value)}, 值={value}")
                    
                    print(f"  💾 GPU内存状态:")
                    gpu_info = get_safe_gpu_info()
                    print(f"    - 已分配: {gpu_info['memory_allocated_gb']:.2f}GB")
                    print(f"    - 已缓存: {gpu_info['memory_reserved_gb']:.2f}GB")
                    print(f"    - GPU利用率: {gpu_info['utilization_percent']}")
                    if gpu_info['device_name'] != 'N/A':
                        print(f"    - 设备: {gpu_info['device_name']}")
                
                batch_start_time = time.time()
                
                # 🚀 PyTorch最佳实践：梯度累积和内存管理
                if batch_idx % effective_accumulation_steps == 0:
                    # 官方推荐：使用set_to_none=True提升性能
                    optimizer.zero_grad(set_to_none=True)
                    
                    # 定期强制清理GPU缓存（防止内存碎片）
                    if batch_idx % (effective_accumulation_steps * 10) == 0:
                        torch.cuda.empty_cache()
                
                # 🔧 记录更新前的不确定性权重
                if args.loss_type == 'combined' and hasattr(criterion, 'uncertainty_weighter'):
                    # 安全访问不确定性权重信息
                    try:
                        uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
                        if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                            pre_update_weights = uncertainty_weighter.get_loss_weights_info()
                            pre_update_log_vars = [param.data.clone() for param in uncertainty_weighter.parameters()]
                    except Exception as e:
                        print(f"警告: 无法获取更新前权重信息: {e}")
                        pre_update_weights = None
                        pre_update_log_vars = None
                else:
                    pre_update_weights = None
                    pre_update_log_vars = None
                
                # 🚀 核心优化：使用外部批量调度处理所有框
                # 准备批量数据（异步传输）
                data_prep_start = time.time()
                embeddings_list = [emb.to(device, non_blocking=True) for emb in data['embeddings']]
                target_masks_list = [mask.to(device, non_blocking=True).float() for mask in data['masks']]
                fidt_maps_list = [fidt.to(device, non_blocking=True).float() for fidt in data['fidt_maps']]  # 新增：FIDT图列表

                if isinstance(data['bboxes'], list):
                    bboxes_list = [bbox.to(device, non_blocking=True) for bbox in data['bboxes']]
                else:
                    bboxes_list = [data['bboxes'][i].to(device, non_blocking=True) for i in range(len(data['embeddings']))]

                class_names_list = data['class_names'] if isinstance(data['class_names'], list) else [data['class_names']] * len(embeddings_list)
                class_ids_list = safe_extract_tensor_items(data['class_ids'], len(embeddings_list))
                box_ids_list = safe_extract_tensor_items(data['box_ids'], len(embeddings_list))
                
                data_prep_time = time.time() - data_prep_start
                if batch_idx % 5 == 0:
                    print(f"  ⚡ 数据准备耗时: {data_prep_time:.3f}s")
                    print(f"  🎯 准备的框数量: {len(embeddings_list)}")
                    print(f"  💾 数据准备后GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB")
                
                # 🚀 外部批量调度处理整个批次的所有框
                inference_start = time.time()
                pred_masks_list = process_batch_boxes_parallel(
                    embeddings_list, target_masks_list, fidt_maps_list, bboxes_list,
                    class_names_list, class_ids_list, box_ids_list,
                    sam, wt_enhancer, sam_transform, args, device
                )
                
                # 🚀 重新构建元数据列表以匹配原始顺序，增加bbox信息
                metadata_list = []
                target_masks_final = []
                for i, pred_mask in enumerate(pred_masks_list):
                    if pred_mask is not None:  # 只处理成功的预测
                        metadata_list.append({
                            'class_name': class_names_list[i],
                            'class_id': class_ids_list[i], 
                            'box_id': box_ids_list[i],
                            'bbox': bboxes_list[i]  # 🚀 新增: 添加bbox信息用于后续bbox_mask计算
                        })
                        target_masks_final.append(target_masks_list[i])
                inference_time = time.time() - inference_start
                
                # 🚀 批量损失计算（训练时不计算详细指标）
                loss_calc_start = time.time()
                batch_loss, batch_box_metrics, batch_loss_breakdown = compute_batch_loss_parallel(
                    pred_masks_list, target_masks_final, metadata_list,
                    args, criterion, loss_aggregator, device, compute_metrics=False  # 训练时关闭指标计算
                )
                loss_calc_time = time.time() - loss_calc_start
                
                # 梯度累积反向传播
                backward_start = time.time()
                normalized_loss = batch_loss / effective_accumulation_steps
                
                if args.use_amp and scaler is not None:
                    scaler.scale(normalized_loss).backward()
                    
                    # 只在累积步骤完成时更新参数
                    if (batch_idx + 1) % effective_accumulation_steps == 0:
                        update_start = time.time()
                        
                        # 🚀 修复的AMP梯度累积逻辑 - 确保scaler状态一致性
                        amp_update_success = False
                        try:
                            # 第一步：检查是否需要梯度裁剪
                            if args.grad_clip > 0:
                                # 安全地unscale梯度以进行梯度裁剪
                                scaler.unscale_(optimizer)
                                torch.nn.utils.clip_grad_norm_(
                                    list(sam.parameters()) + 
                                    (list(wt_enhancer.parameters()) if args.use_wt_enhancer and wt_enhancer else []),
                                    args.grad_clip
                                )
                            
                            # 第二步：执行优化器步骤
                            scaler.step(optimizer)
                            amp_update_success = True
                            
                            # 检查scaler的状态
                            current_scale = scaler.get_scale()
                            if current_scale < 1.0:
                                logger.log(f"警告: AMP scaler缩放因子为 {current_scale}, 存在数值不稳定")
                            
                        except RuntimeError as e:
                            error_msg = str(e)
                            logger.log(f"AMP步骤异常: {error_msg}")
                            
                            if "unscale_() has already been called" in error_msg:
                                logger.log(f"🔧 检测到scaler状态不一致，强制重置scaler")
                                # 强制重置scaler并清除梯度
                                scaler = torch.cuda.amp.GradScaler()
                                optimizer.zero_grad(set_to_none=True)
                                amp_update_success = False
                            elif "No inf checks were recorded" in error_msg:
                                logger.log(f"🔧 检测到scaler状态异常，重置scaler")
                                scaler = torch.cuda.amp.GradScaler()
                                amp_update_success = False
                            elif any(keyword in error_msg.lower() for keyword in ["inf", "nan", "overflow"]):
                                logger.log(f"🔢 检测到数值溢出，将跳过此次更新")
                                amp_update_success = False
                            else:
                                logger.log(f"❌ 未知AMP错误: {e}")
                                raise e
                        
                        # 第三步：必须调用scaler.update()来重置状态
                        try:
                            scaler.update()
                            update_time = time.time() - update_start
                            
                            # 记录更新结果
                            if amp_update_success:
                                logger.log(f"✅ AMP更新成功 (batch {batch_idx+1})")
                            else:
                                logger.log(f"⚠️ AMP更新跳过，但scaler状态已重置 (batch {batch_idx+1})")
                                
                        except Exception as update_error:
                            logger.log(f"🚨 scaler.update()失败: {update_error}")
                            # 如果update也失败，完全重新创建scaler
                            scaler = torch.cuda.amp.GradScaler()
                            update_time = 0.0
                        
                        # 确保下一轮累积开始时梯度已清零
                        optimizer.zero_grad(set_to_none=True)
                        
                        # 🔧 在参数更新后检查不确定性权重变化
                        if args.loss_type == 'combined' and pre_update_weights is not None and pre_update_log_vars is not None:
                            check_and_log_uncertainty_weights_update(
                                batch_idx, criterion, pre_update_weights, pre_update_log_vars, 
                                batch_loss_breakdown if 'batch_loss_breakdown' in locals() else None
                            )
                        
                        update_time = 0.0
                else:
                    normalized_loss.backward()
                    
                    # 只在累积步骤完成时更新参数
                    if (batch_idx + 1) % effective_accumulation_steps == 0:
                        update_start = time.time()
                        if args.grad_clip > 0:
                            torch.nn.utils.clip_grad_norm_(
                                list(sam.parameters()) + 
                                (list(wt_enhancer.parameters()) if args.use_wt_enhancer and wt_enhancer else []),
                                args.grad_clip
                            )
                        optimizer.step()
                        scheduler.step()  # 在优化器步骤之后立即调用学习率调度器
                        update_time = time.time() - update_start
                        
                        # 🔧 在参数更新后检查不确定性权重变化 (非AMP版本)
                        if args.loss_type == 'combined' and hasattr(criterion, 'uncertainty_weighter') and pre_update_weights is not None and pre_update_log_vars is not None:
                            try:
                                uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
                                if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                                    post_update_weights = uncertainty_weighter.get_loss_weights_info()
                                    post_update_log_vars = [param.data.clone() for param in uncertainty_weighter.parameters()]
                                    
                                    # 检查是否有实际更新
                                    weights_changed = False
                                    for i, (pre, post) in enumerate(zip(pre_update_log_vars, post_update_log_vars)):
                                        if pre.numel() == 1 and post.numel() == 1:
                                            if abs(pre.item() - post.item()) > 1e-8:
                                                weights_changed = True
                                                break
                                        else:
                                            if torch.any(torch.abs(pre - post) > 1e-8):
                                                weights_changed = True
                                                break
                                    
                                    if weights_changed:
                                        print(f"    🎯 不确定性权重更新成功 (batch {batch_idx+1}):")
                                        for i, (pre, post) in enumerate(zip(pre_update_log_vars, post_update_log_vars)):
                                            if pre.numel() == 1 and post.numel() == 1:
                                                print(f"      log_var[{i}]: {pre.item():.8f} → {post.item():.8f} (Δ={post.item()-pre.item():+.8f})")
                                            else:
                                                pre_val = pre.flatten()[0].item()
                                                post_val = post.flatten()[0].item()
                                                print(f"      log_var[{i}][0]: {pre_val:.8f} → {post_val:.8f} (Δ={post_val-pre_val:+.8f})")
                                        print(f"      权重: DiceFocal {pre_update_weights['dice_focal_weight']:.6f}→{post_update_weights['dice_focal_weight']:.6f}, AUC {pre_update_weights['auc_weight']:.6f}→{post_update_weights['auc_weight']:.6f}")
                                    else:
                                        print(f"    ⚠️ 不确定性权重未更新 (batch {batch_idx+1}) - 检查梯度和学习率")
                                        # 检查梯度
                                        for i, param in enumerate(uncertainty_weighter.parameters()):
                                            if param.grad is not None:
                                                if param.grad.numel() == 1:
                                                    print(f"      log_var[{i}] 梯度: {param.grad.item():.8f}")
                                                else:
                                                    print(f"      log_var[{i}] 梯度: {param.grad.flatten()[0].item():.8f} (第一个元素)")
                                            else:
                                                print(f"      log_var[{i}] 梯度: None")
                            except Exception as e:
                                print(f"    ❌ 权重更新检查失败: {e}")
                    else:
                        update_time = 0.0
                backward_time = time.time() - backward_start
                
                # 记录结果（训练阶段只记录损失，不计算详细指标以提升速度）
                train_losses.append(batch_loss.item())
                
                # 只记录基本的框信息，不计算详细指标
                for box_metrics in batch_box_metrics:
                    # 只保留类别信息，用于epoch结束时的指标计算
                    train_per_box_metrics.append({
                        'class_name': box_metrics['class_name'],
                        'box_id': box_metrics['box_id'],
                        'loss_breakdown': box_metrics.get('loss_breakdown', {}),
                        'metrics': None  # 训练阶段暂不计算详细指标
                    })
                
                # 🔍 详细性能分析
                total_batch_time = time.time() - batch_start_time
                if batch_idx % 5 == 0:
                    print(f"\n  ⏱️  Batch {batch_idx} 性能分析:")
                    print(f"    - 数据准备: {data_prep_time:.3f}s ({data_prep_time/total_batch_time*100:.1f}%)")
                    print(f"    - 推理计算: {inference_time:.3f}s ({inference_time/total_batch_time*100:.1f}%)")
                    print(f"    - 损失计算: {loss_calc_time:.3f}s ({loss_calc_time/total_batch_time*100:.1f}%)")
                    print(f"    - 反向传播: {backward_time:.3f}s ({backward_time/total_batch_time*100:.1f}%)")
                    if update_time > 0:
                        print(f"    - 参数更新: {update_time:.3f}s ({update_time/total_batch_time*100:.1f}%)")
                    print(f"    - 总计: {total_batch_time:.3f}s")
                    print(f"    - 🚀 理论最大批处理加速比: {len(embeddings_list)}x (如果完全并行)")
                    print(f"    - ⚡ 实际每框平均耗时: {total_batch_time/len(embeddings_list):.3f}s")
                    print(f"    - 💾 内存效率: {torch.cuda.memory_allocated()/1024**3:.2f}GB allocated")
                
                # 更新进度条（包含详细性能信息）
                if batch_idx % 10 == 0:  # 每10个batch更新一次，减少开销
                    try:
                        # 使用安全的GPU信息获取
                        gpu_info = get_safe_gpu_info()
                        
                        postfix_dict = {
                            'loss': f"{batch_loss:.3f}",
                            'boxes': len(embeddings_list),
                            'time': f"{total_batch_time:.2f}s",
                            'avg_per_box': f"{total_batch_time/len(embeddings_list):.3f}s",
                            'GPU%': gpu_info['utilization_percent'],
                            'mem': f"{gpu_info['memory_allocated_gb']:.1f}GB"
                        }
                        
                        # 安全的进度条更新
                        if hasattr(train_pbar, 'set_postfix'):
                            train_pbar.set_postfix(postfix_dict)
                    except Exception:
                        pass  # 忽略进度条更新错误，专注于训练性能
                
                # 限制调试样本数量
                if args.debug and batch_idx >= 20:
                    break
                
                # 极大减少内存清理频率，提升训练速度
                if batch_idx % 500 == 0:  # 每500个batch清理一次
                    torch.cuda.empty_cache()
                
                # 移除不必要的同步操作以提升异步性能
                # 注释掉同步操作以提升并发性能
                # if torch.cuda.is_available():
                #     torch.cuda.synchronize()

            except Exception as e:
                logger.log(f"训练批次错误 {batch_idx}: {e}")
                
                # 添加更详细的错误信息和调试
                import traceback
                error_details = traceback.format_exc()
                logger.log(f"详细错误堆栈: {error_details}")
                
                # 🔍 增强的错误分析和调试信息
                gpu_info = get_safe_gpu_info()
                logger.log(f"🔍 错误时系统状态:")
                logger.log(f"  - GPU内存已分配: {gpu_info['memory_allocated_gb']:.2f}GB")
                logger.log(f"  - GPU内存缓存: {gpu_info['memory_reserved_gb']:.2f}GB")
                logger.log(f"  - GPU利用率: {gpu_info['utilization_percent']}")
                logger.log(f"  - GPU设备: {gpu_info['device_name']}")
                logger.log(f"  - 批次索引: {batch_idx}")
                logger.log(f"  - 累积步数: {effective_accumulation_steps}")
                logger.log(f"  - AMP启用: {args.use_amp}")
                logger.log(f"  - 当前学习率: {optimizer.param_groups[0]['lr']:.2e}")
                
                # 🔍 错误类型分析
                error_msg = str(e).lower()
                if "cuda" in error_msg and "memory" in error_msg:
                    logger.log(f"🚨 检测到CUDA内存不足错误")
                    logger.log(f"  建议: 减少batch_size或启用梯度检查点")
                elif "pynvml" in error_msg:
                    logger.log(f"📊 GPU监控库问题，不影响训练")
                elif "inf" in error_msg or "nan" in error_msg:
                    logger.log(f"🔢 检测到数值不稳定问题")
                    logger.log(f"  建议: 降低学习率或启用梯度裁剪")
                elif "amp" in error_msg or "scaler" in error_msg:
                    logger.log(f"⚡ 混合精度训练问题")
                    logger.log(f"  建议: 检查梯度缩放或禁用AMP")
                else:
                    logger.log(f"❓ 未知错误类型，需要进一步调试")
                
                # 内存错误时强制清理
                torch.cuda.empty_cache()
                
                # 如果是关键错误（如CUDA OOM），记录更多信息
                if "CUDA" in str(e) or "memory" in str(e).lower():
                    logger.log(f"🧹 检测到CUDA/内存相关错误，执行强制清理")
                    # 等待GPU操作完成
                    if torch.cuda.is_available():
                        torch.cuda.synchronize()
                        # 额外的清理步骤
                        import gc
                        gc.collect()
                        logger.log(f"🧹 强制清理完成，继续训练")
                
                    continue
                
        # Epoch结束后：统一计算训练指标
        logger.log(f"📊 Epoch结束，开始统一计算训练指标 (Epoch {epoch+1})...")
        
        # 记录当前GPU状态用于调试
        gpu_info = get_safe_gpu_info()
        logger.log(f"💾 指标计算前GPU状态: 内存={gpu_info['memory_allocated_gb']:.2f}GB, 利用率={gpu_info['utilization_percent']}")
        
        # 🔍 显示不确定性权重变化情况（任务2实现）
        display_uncertainty_weights_info(epoch, args, criterion, logger)
        
        # 🔍 额外的详细权重监控（每个epoch结束时）
        if args.loss_type == 'combined' and hasattr(criterion, 'uncertainty_weighter'):
            try:
                uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
                if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                    current_weights = uncertainty_weighter.get_loss_weights_info()
                    print(f"\n📊 === Epoch {epoch+1} 不确定性权重详细监控 ===")
                    print(f"🔢 原始log_vars参数值:")
                    
                    # 安全访问log_vars参数
                    uncertainty_params = list(uncertainty_weighter.parameters())
                    if len(uncertainty_params) >= 1:
                        # 修复多元素张量访问
                        for i, param in enumerate(uncertainty_params):
                            if param.numel() == 1:
                                print(f"   log_var[{i}]: {param.item():.6f}")
                            else:
                                # 多元素张量，逐个显示
                                for j in range(min(param.numel(), 2)):  # 最多显示前2个元素
                                    print(f"   log_var[{i}][{j}]: {param.flatten()[j].item():.6f}")
                        print(f"💡 解释: log_vars越大，该任务的不确定性越高，权重越小")
                        
                        print(f"⚖️ 计算得出的权重:")
                        print(f"   DiceFocal权重: {current_weights['dice_focal_weight']:.6f}")
                        print(f"   AUC权重: {current_weights['auc_weight']:.6f}")
                        print(f"🎯 权重影响:")
                        if current_weights['dice_focal_weight'] > current_weights['auc_weight']:
                            print(f"   DiceFocal损失主导训练（形状精度优先）")
                        else:
                            print(f"   AUC损失主导训练（排序质量优先）")
                        print(f"=" * 60)
                else:
                    print(f"⚠️ 不确定性权重器缺少get_loss_weights_info方法")
            except Exception as weight_error:
                print(f"❌ 权重监控失败: {weight_error}")
                import traceback
                print(f"详细错误: {traceback.format_exc()}")
        sam.eval()  # 切换到评估模式计算指标
        if args.use_wt_enhancer and wt_enhancer:
            wt_enhancer.eval()
        train_per_box_metrics = []
        
        metrics_calc_start = time.time()
        with torch.no_grad():
            # 采样少量数据用于训练指标计算（大幅减少计算量）
            from torch.utils.data import Subset
            if args.debug:
                # 调试模式：只取前50个batch
                indices = list(range(0, min(len(train_dataset), 50 * effective_batch_size), 10))
            else:
                # 正常模式：采样1/8的数据（进一步减少）
                indices = list(range(0, len(train_dataset), 8))
            
            train_subset = Subset(train_dataset, indices)
            # 🚀 指标计算专用高性能DataLoader配置
            # 参考PyTorch社区最佳实践，指标计算阶段可以使用更激进的配置
            metric_dataloader_config = dataloader_config.copy()
            metric_dataloader_config.update({
                'num_workers': 0,            # 单进程，避免序列化 dataset 对象
                'timeout': 0,               # 无超时
                'shuffle': False,
                'persistent_workers': False,
                'pin_memory': False,
                'drop_last': False
            })
            # 单进程时不能设置multiprocessing_context
            if 'multiprocessing_context' in metric_dataloader_config:
                del metric_dataloader_config['multiprocessing_context']
            
            train_metric_loader = DataLoader(train_subset, **metric_dataloader_config)
            
            logger.log(f"📊 训练指标采样: {len(indices)}/{len(train_dataset)} 个框 ({len(indices)/len(train_dataset)*100:.1f}%) - 训练集指标为估算值")
            logger.log(f"🚀 指标计算DataLoader优化配置:")
            logger.log(f"  - Workers: {metric_dataloader_config['num_workers']} (训练时: {optimal_workers})")
            logger.log(f"  - Timeout: {metric_dataloader_config['timeout']}s (训练时: {dataloader_config['timeout']}s)")
            logger.log(f"  - 优化策略: 指标计算时I/O密集，增加并发和预取")
            
            metric_batches_processed = 0
            for batch_idx, data in enumerate(tqdm(train_metric_loader, desc="🔍计算训练指标")):
                try:
                    # 数据准备（简化版本）
                    embeddings_list = [emb.to(device, non_blocking=True) for emb in data['embeddings']]
                    target_masks_list = [mask.to(device, non_blocking=True).float() for mask in data['masks']]
                    
                    if isinstance(data['bboxes'], list):
                        bboxes_list = [bbox.to(device, non_blocking=True) for bbox in data['bboxes']]
                    else:
                        bboxes_list = [data['bboxes'][i].to(device, non_blocking=True) for i in range(len(data['embeddings']))]
                    
                    class_names_list = data['class_names'] if isinstance(data['class_names'], list) else [data['class_names']] * len(embeddings_list)
                    class_ids_list = safe_extract_tensor_items(data['class_ids'], len(embeddings_list))
                    box_ids_list = safe_extract_tensor_items(data['box_ids'], len(embeddings_list))
                    
                    # 🚀 优化的推理和指标计算
                    pred_masks_list = process_batch_boxes_parallel(
                        embeddings_list, target_masks_list, bboxes_list,
                        class_names_list, class_ids_list, box_ids_list,
                        sam, wt_enhancer, sam_transform, args, device
                    )
                    
                    # 🚀 批量指标计算（只在epoch结束时进行）- 使用bbox_mask
                    for i, pred_mask in enumerate(pred_masks_list):
                        if pred_mask is not None:
                            metadata = {
                                'class_name': class_names_list[i],
                                'class_id': class_ids_list[i],
                                'box_id': box_ids_list[i],
                                'bbox': bboxes_list[i]  # 🚀 添加bbox信息
                            }
                            
                            # 统一使用 calculate_per_box_metrics 函数，确保训练和测试阶段一致性
                            pred_prob = torch.sigmoid(pred_mask)
                            target_mask = target_masks_list[i]
                            
                            # 🚀 生成bbox_mask，确保指标计算限制在框内
                            bbox_mask = create_bbox_mask(
                                mask_shape=pred_prob.shape,
                                bbox=metadata['bbox'],
                                device=device
                            )
                            
                            # 🚀 应用bbox_mask到目标掩码
                            target_mask_roi = target_mask * bbox_mask
                            
                            # 使用统一的指标计算函数，确保训练和测试阶段的一致性
                            with torch.no_grad():
                                box_metrics = calculate_per_box_metrics(
                                    pred_prob, target_mask_roi, metadata['class_id'],
                                    debug=args.debug if hasattr(args, 'debug') else False,
                                    bbox_mask=bbox_mask  # 🚀 传入bbox_mask确保彻底约束
                                )
                            
                            # 保存完整的指标结果
                            if box_metrics is not None:
                                train_per_box_metrics.append({
                                    'class_name': metadata['class_name'],
                                    'class_id': metadata['class_id'],  # 添加class_id
                                    'box_id': metadata['box_id'],
                                    'metrics': box_metrics  # 使用完整的指标字典
                                })
                    
                    metric_batches_processed += 1
                    if args.debug and metric_batches_processed >= 3:  # 调试模式进一步限制
                        break
                
                except Exception as e:
                    logger.log(f"❌ 训练指标计算错误 {batch_idx}: {e}")
                    import traceback
                    logger.log(f"详细错误: {traceback.format_exc()}")
                    continue

        metrics_calc_time = time.time() - metrics_calc_start
        logger.log(f"⏱️ 训练指标计算完成，耗时: {metrics_calc_time:.2f}s，处理了{len(train_per_box_metrics)}个框")
        
        sam.train()  # 切换回训练模式
        if args.use_wt_enhancer and wt_enhancer:
            wt_enhancer.train()
        
        # 聚合训练指标
        train_aggregated = aggregate_metrics_across_boxes(train_per_box_metrics, CLASSES)
        
        # 计算平均训练损失（修复损失聚合逻辑）
        if args.loss_type == 'combined':
            # 联合损失模式：直接使用train_losses的平均值（已包含正确的α/β权重）
            avg_train_loss = np.mean(train_losses) if train_losses else 0.0
            logger.log(f"  ✅ 联合损失模式: 直接使用联合损失均值={avg_train_loss:.4f}")
        elif loss_aggregator is not None:
            # 纯AUC损失模式：使用聚合器进行类别权重聚合
            auc_stats = loss_aggregator.get_detailed_stats()
            logger.log(f"  AUC损失统计: 总框数={auc_stats.get('total_boxes', 0)}")
            for cls_name, avg_loss in auc_stats.get('per_class_avg_loss', {}).items():
                logger.log(f"    {cls_name}: 平均损失={avg_loss:.4f}, 框数={auc_stats['per_class_boxes'].get(cls_name, 0)}")
            
            # 计算聚合后的损失作为平均训练损失
            aggregated_loss = loss_aggregator.aggregate_and_reset()
            avg_train_loss = aggregated_loss.item() if aggregated_loss is not None else 0.0
        else:
            # DiceFocal或其他损失模式
            avg_train_loss = np.mean(train_losses) if train_losses else 0.0
            
        # 计算基于不确定性加权的损失分解（如果使用联合损失）
        if args.loss_type == 'combined':
            # 修复：直接从训练循环中累积的损失分解信息，而不是依赖于保存在训练指标中的信息
            # 因为训练指标计算阶段是专门用于指标计算的，不包含损失分解信息
            
            # 从训练循环中累积的损失分解信息
            all_df_losses = []
            all_auc_losses = []
            
            # 遍历所有训练批次的损失分解信息（如果存在）
            if hasattr(loss_aggregator, 'get_detailed_stats') and loss_aggregator:
                auc_stats = loss_aggregator.get_detailed_stats()
                # AUC损失统计
                for cls_name, avg_loss in auc_stats.get('per_class_avg_loss', {}).items():
                    all_auc_losses.append(avg_loss)
            
            # 如果没有详细的分解信息，使用训练损失的估算
            avg_df_loss = np.mean(all_df_losses) if all_df_losses else 0.0
            avg_auc_loss = np.mean(all_auc_losses) if all_auc_losses else 0.0
            
            # 如果两个都是0（通常在首次运行时），使用训练损失的大致分配估算
            if avg_df_loss == 0.0 and avg_auc_loss == 0.0 and avg_train_loss > 0:
                # 使用权重信息估算分配
                uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
                if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                    try:
                        loss_weights_info = uncertainty_weighter.get_loss_weights_info()
                        df_weight = loss_weights_info.get('dice_focal_weight', 0.5)
                        auc_weight = loss_weights_info.get('auc_weight', 0.5)
                        # 根据权重估算各损失分量
                        avg_df_loss = avg_train_loss * df_weight
                        avg_auc_loss = avg_train_loss * auc_weight
                    except Exception:
                        # 如果权重获取失败，使用50-50分配
                        avg_df_loss = avg_train_loss * 0.5
                        avg_auc_loss = avg_train_loss * 0.5
            
            # 获取不确定性权重信息
            uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
            if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                try:
                    loss_weights_info = uncertainty_weighter.get_loss_weights_info()
                except Exception:
                    loss_weights_info = {
                        'dice_focal_uncertainty': 1.0,
                        'auc_uncertainty': 1.0,
                        'dice_focal_weight': 0.5,
                        'auc_weight': 0.5
                    }
            else:
                loss_weights_info = {
                    'dice_focal_uncertainty': 1.0,
                    'auc_uncertainty': 1.0,
                    'dice_focal_weight': 0.5,
                    'auc_weight': 0.5
                }
            
            logger.log(f"  💡 基于不确定性的损失分解统计:")
            logger.log(f"    联合损失均值 (Per-Batch): {avg_train_loss:.4f}")
            logger.log(f"    平均DiceFocal损失 (Per-Box): {avg_df_loss:.4f}")
            logger.log(f"    平均AUC损失 (Per-Box): {avg_auc_loss:.4f}")
            
            logger.log(f"    📊 学习到的权重 (Epoch {epoch+1}):")
            logger.log(f"      - 不确定性(σ): DiceFocal={loss_weights_info['dice_focal_uncertainty']:.4f}, AUC={loss_weights_info['auc_uncertainty']:.4f}")
            logger.log(f"      - 损失权重: DiceFocal={loss_weights_info['dice_focal_weight']:.4f}, AUC={loss_weights_info['auc_weight']:.4f}")
            
            # 分析权重比例
            weight_ratio = loss_weights_info['auc_weight'] / loss_weights_info['dice_focal_weight'] if loss_weights_info['dice_focal_weight'] > 0 else 0
            logger.log(f"      - 权重比例 (AUC/DiceFocal): {weight_ratio:.3f}")
            
            if loss_weights_info['dice_focal_uncertainty'] > loss_weights_info['auc_uncertainty']:
                logger.log(f"    💭 解释: DiceFocal任务不确定性更高，模型对其预测更不自信")
            else:
                logger.log(f"    💭 解释: AUC任务不确定性更高，模型对其预测更不自信")
            
            if class_weights is not None:
                logger.log(f"    类别权重: {[f'{w:.2f}' for w in class_weights.tolist()]}")
        
        # 测试阶段
        logger.log(f"📊 开始测试 (Epoch {epoch+1})...")
        sam.eval()

        # 设置测试模式标志（用于确定性点提示采样）
        args.is_training_mode = False

        test_per_box_metrics = []
        test_losses = []
        
        # 显示进度条
        test_pbar = tqdm(test_loader, desc=f"测试 Epoch {epoch+1}/{args.num_epochs}")
        
        with torch.no_grad():
            for batch_idx, data in enumerate(test_pbar):
                try:
                    # 准备批量数据
                    embeddings_list = [emb.to(device, non_blocking=True) for emb in data['embeddings']]
                    target_masks_list = [mask.to(device, non_blocking=True).float() for mask in data['masks']]
                    fidt_maps_list = [fidt.to(device, non_blocking=True).float() for fidt in data['fidt_maps']]  # 新增：FIDT图列表

                    if isinstance(data['bboxes'], list):
                        bboxes_list = [bbox.to(device, non_blocking=True) for bbox in data['bboxes']]
                    else:
                        bboxes_list = [data['bboxes'][i].to(device, non_blocking=True) for i in range(len(data['embeddings']))]

                    class_names_list = data['class_names'] if isinstance(data['class_names'], list) else [data['class_names']] * len(embeddings_list)
                    class_ids_list = safe_extract_tensor_items(data['class_ids'], len(embeddings_list))
                    box_ids_list = safe_extract_tensor_items(data['box_ids'], len(embeddings_list))
                    
                    # 并行处理整个批次的所有框（确保box by box模式）
                    if args.debug:
                        logger.log(f"🔍 测试批次 {batch_idx}: 处理 {len(embeddings_list)} 个框")
                        for i, (class_name, box_id) in enumerate(zip(class_names_list, box_ids_list)):
                            logger.log(f"  框 {i+1}: {class_name} (box_id: {box_id})")
                    
                    pred_masks_list = process_batch_boxes_parallel(
                        embeddings_list, target_masks_list, fidt_maps_list, bboxes_list,
                        class_names_list, class_ids_list, box_ids_list,
                        sam, wt_enhancer, sam_transform, args, device
                    )
                    
                    # 🚀 重新构建元数据列表以匹配原始顺序，增加bbox信息（测试阶段）
                    metadata_list = []
                    target_masks_final = []
                    for i, pred_mask in enumerate(pred_masks_list):
                        if pred_mask is not None:  # 只处理成功的预测
                            metadata_list.append({
                                'class_name': class_names_list[i],
                                'class_id': class_ids_list[i], 
                                'box_id': box_ids_list[i],
                                'bbox': bboxes_list[i]  # 🚀 新增: 添加bbox信息用于测试阶段的bbox_mask计算
                            })
                            target_masks_final.append(target_masks_list[i])
                    
                    # 批量损失计算和指标计算（测试时计算详细指标）
                    batch_loss, batch_box_metrics, batch_loss_breakdown = compute_batch_loss_parallel(
                        pred_masks_list, target_masks_final, metadata_list,
                        args, criterion, loss_aggregator, device, compute_metrics=True  # 测试时开启指标计算
                    )
                    
                    # Debug信息：显示指标计算结果
                    if args.debug:
                        logger.log(f"  批次 {batch_idx} 结果: 损失={batch_loss:.4f}, 成功计算指标的框数={len(batch_box_metrics)}")
                        if len(batch_box_metrics) > 0:
                            # 显示前几个框的指标
                            for i, box_metrics in enumerate(batch_box_metrics[:3]):  # 只显示前3个
                                metrics = box_metrics.get('metrics', {})
                                logger.log(f"    框 {i+1} ({box_metrics.get('class_name', 'Unknown')}): Dice={metrics.get('dice', 0):.4f}, IoU={metrics.get('iou', 0):.4f}")
                        else:
                            logger.log(f"    ❌ 警告: 没有成功计算的指标")
                    
                    # 记录结果
                    test_losses.append(batch_loss.item())
                    test_per_box_metrics.extend(batch_box_metrics)
                    
                    # 测试阶段进度条更新（修复boxes显示为0的问题）
                    if batch_idx % 20 == 0:  # 测试阶段更新频率更低
                        try:
                            # 使用实际处理的框数量，而不是成功计算指标的框数量
                            total_boxes_processed = len(embeddings_list)
                            successful_boxes = len(batch_box_metrics)
                            
                            postfix_dict = {
                                'loss': f"{batch_loss:.3f}",
                                'boxes': total_boxes_processed,  # 实际处理的框数
                                'success': successful_boxes      # 成功计算指标的框数
                            }
                            
                            if hasattr(test_pbar, 'set_postfix'):
                                test_pbar.set_postfix(postfix_dict)
                        except Exception:
                            pass
                    
                    # 限制调试测试样本数量
                    if args.debug and batch_idx >= 10:
                        break

                except Exception as e:
                    logger.log(f"测试批次错误 {batch_idx}: {e}")
                    
                    # 🔍 测试阶段增强的错误信息
                    import traceback
                    error_details = traceback.format_exc()
                    logger.log(f"测试阶段详细错误堆栈: {error_details}")
                    
                    # 🔍 测试阶段错误分析
                    gpu_info = get_safe_gpu_info()
                    logger.log(f"🔍 测试错误时系统状态:")
                    logger.log(f"  - GPU内存已分配: {gpu_info['memory_allocated_gb']:.2f}GB")
                    logger.log(f"  - GPU内存缓存: {gpu_info['memory_reserved_gb']:.2f}GB")
                    logger.log(f"  - GPU利用率: {gpu_info['utilization_percent']}")
                    logger.log(f"  - 测试批次索引: {batch_idx}")
                    logger.log(f"  - 模型状态: 评估模式")
                    
                    # 错误类型分析
                    error_msg = str(e).lower()
                    if "cuda" in error_msg and "memory" in error_msg:
                        logger.log(f"🚨 测试阶段CUDA内存不足")
                    elif "pynvml" in error_msg:
                        logger.log(f"📊 GPU监控问题（测试阶段）")
                    else:
                        logger.log(f"❓ 测试阶段未知错误")
                    
                    # 清理GPU缓存
                    torch.cuda.empty_cache()
                    continue

        # 聚合测试指标
        test_aggregated = aggregate_metrics_across_boxes(test_per_box_metrics, CLASSES)
        avg_test_loss = np.mean(test_losses) if test_losses else 0.0
        
        # 详细打印测试阶段的所有指标
        logger.log(f"\n" + "="*100)
        logger.log(f"📊 Epoch {epoch+1} 测试阶段详细指标报告")
        logger.log(f"="*100)
        
        # 1. 测试数据统计
        test_summary = test_aggregated.get('summary', {})
        logger.log(f"📋 测试数据统计:")
        logger.log(f"  总处理框数: {test_summary.get('total_boxes', 0)}")
        logger.log(f"  平均损失: {avg_test_loss:.6f}")
        
        if test_summary.get('boxes_per_class'):
            logger.log(f"  按类别框数分布:")
            for cls_name, count in test_summary['boxes_per_class'].items():
                logger.log(f"    {cls_name}: {count} 个框")
        
        # 2. 整体指标 (完整数据)
        test_overall = test_aggregated['overall']
        logger.log(f"\n🎯 测试集整体指标 (完整数据):")
        logger.log(f"  📐 基础分割指标:")
        logger.log(f"    Dice 系数: {test_overall.get('dice', 0):.6f}")
        logger.log(f"    IoU/Jaccard: {test_overall.get('iou', 0):.6f}")
        logger.log(f"  🏥 边界与形状指标:")
        logger.log(f"    Surface Dice: {test_overall.get('surface_dice', 0):.6f}")
        logger.log(f"    Boundary IoU: {test_overall.get('boundary_iou', 0):.6f}")
        logger.log(f"    Hausdorff Distance 95%: {test_overall.get('hd95', 0):.4f}")
        logger.log(f"  📈 分类性能指标:")
        logger.log(f"    精确率 (Precision): {test_overall.get('precision', 0):.6f}")
        logger.log(f"    召回率 (Recall): {test_overall.get('recall', 0):.6f}")
        logger.log(f"    F1-Score: {test_overall.get('f1', 0):.6f}")
        logger.log(f"    特异性 (Specificity): {test_overall.get('specificity', 0):.6f}")
        logger.log(f"  ⚖️ 准确率指标:")
        logger.log(f"    准确率 (Accuracy): {test_overall.get('accuracy', 0):.6f}")
        logger.log(f"    平衡准确率 (Balanced Acc): {test_overall.get('balanced_accuracy', 0):.6f}")
        logger.log(f"  ❌ 错误率分析:")
        logger.log(f"    假阳性率 (FPR): {test_overall.get('fpr', 0):.6f}")
        logger.log(f"    假阴性率 (FNR): {test_overall.get('fnr', 0):.6f}")
        
        # 3. 按类别详细指标
        test_per_class = test_aggregated.get('per_class', {})
        if test_per_class:
            logger.log(f"\n📊 按类别详细指标 (测试集完整数据):")
            for cls_name in CLASSES:
                if cls_name in test_per_class:
                    cls_metrics = test_per_class[cls_name]
                    logger.log(f"\n  📌 类别: {cls_name}")
                    logger.log(f"    📐 基础分割:")
                    logger.log(f"      Dice: {cls_metrics.get('dice', 0):.6f}")
                    logger.log(f"      IoU: {cls_metrics.get('iou', 0):.6f}")
                    logger.log(f"      Jaccard: {cls_metrics.get('jaccard', 0):.6f}")
                    logger.log(f"    🏥 边界与形状:")
                    logger.log(f"      Surface Dice: {cls_metrics.get('surface_dice', 0):.6f}")
                    logger.log(f"      Boundary IoU: {cls_metrics.get('boundary_iou', 0):.6f}")
                    logger.log(f"      HD95: {cls_metrics.get('hd95', 0):.4f}")
                    logger.log(f"    📈 分类性能:")
                    logger.log(f"      Precision: {cls_metrics.get('precision', 0):.6f}")
                    logger.log(f"      Recall: {cls_metrics.get('recall', 0):.6f}")
                    logger.log(f"      F1-Score: {cls_metrics.get('f1', 0):.6f}")
                    logger.log(f"      Specificity: {cls_metrics.get('specificity', 0):.6f}")
                    logger.log(f"    ⚖️ 准确率:")
                    logger.log(f"      Accuracy: {cls_metrics.get('accuracy', 0):.6f}")
                    logger.log(f"      Balanced Acc: {cls_metrics.get('balanced_accuracy', 0):.6f}")
                    logger.log(f"    ❌ 错误率:")
                    logger.log(f"      FPR: {cls_metrics.get('fpr', 0):.6f}")
                    logger.log(f"      FNR: {cls_metrics.get('fnr', 0):.6f}")
                else:
                    logger.log(f"\n  📌 类别: {cls_name} - 无数据")
        
        # 4. 训练集指标 (采样估算)
        logger.log(f"\n🔥 训练集指标对比 (1/8采样估算值):")
        train_overall = train_aggregated['overall']
        logger.log(f"  📐 基础分割指标:")
        logger.log(f"    Dice 系数: {train_overall.get('dice', 0):.6f}")
        logger.log(f"    IoU/Jaccard: {train_overall.get('iou', 0):.6f}")
        logger.log(f"  🏥 边界与形状指标:")
        logger.log(f"    Surface Dice: {train_overall.get('surface_dice', 0):.6f}")
        logger.log(f"    Boundary IoU: {train_overall.get('boundary_iou', 0):.6f}")
        logger.log(f"    Hausdorff Distance 95%: {train_overall.get('hd95', 0):.4f}")
        logger.log(f"  📈 分类性能指标:")
        logger.log(f"    精确率 (Precision): {train_overall.get('precision', 0):.6f}")
        logger.log(f"    召回率 (Recall): {train_overall.get('recall', 0):.6f}")
        logger.log(f"    F1-Score: {train_overall.get('f1', 0):.6f}")
        logger.log(f"    特异性 (Specificity): {train_overall.get('specificity', 0):.6f}")
        
        # 5. 按类别训练集指标
        train_per_class = train_aggregated.get('per_class', {})
        if train_per_class:
            logger.log(f"\n📊 按类别训练集指标 (1/8采样估算值):")
            for cls_name in CLASSES:
                if cls_name in train_per_class:
                    cls_metrics = train_per_class[cls_name]
                    logger.log(f"\n  📌 类别: {cls_name}")
                    logger.log(f"    📐 基础分割:")
                    logger.log(f"      Dice: {cls_metrics.get('dice', 0):.6f}")
                    logger.log(f"      IoU: {cls_metrics.get('iou', 0):.6f}")
                    logger.log(f"      Jaccard: {cls_metrics.get('jaccard', 0):.6f}")
                    logger.log(f"    🏥 边界与形状:")
                    logger.log(f"      Surface Dice: {cls_metrics.get('surface_dice', 0):.6f}")
                    logger.log(f"      Boundary IoU: {cls_metrics.get('boundary_iou', 0):.6f}")
                    logger.log(f"      HD95: {cls_metrics.get('hd95', 0):.4f}")
                    logger.log(f"    📈 分类性能:")
                    logger.log(f"      Precision: {cls_metrics.get('precision', 0):.6f}")
                    logger.log(f"      Recall: {cls_metrics.get('recall', 0):.6f}")
                    logger.log(f"      F1-Score: {cls_metrics.get('f1', 0):.6f}")
                    logger.log(f"      Specificity: {cls_metrics.get('specificity', 0):.6f}")
                else:
                    logger.log(f"\n  📌 类别: {cls_name} - 无数据")
        
        logger.log(f"\n" + "="*100)
        train_per_class = train_aggregated.get('per_class', {})
        logger.log(f"\n🎯 训练集整体指标 (1/8采样估算值):")
        logger.log(f"  📐 基础分割指标:")
        logger.log(f"    Dice 系数: {train_overall.get('dice', 0):.6f}")
        logger.log(f"    IoU/Jaccard: {train_overall.get('iou', 0):.6f}")
        logger.log(f"  🏥 边界与形状指标:")
        logger.log(f"    Surface Dice: {train_overall.get('surface_dice', 0):.6f}")
        logger.log(f"    Boundary IoU: {train_overall.get('boundary_iou', 0):.6f}")
        logger.log(f"    Hausdorff Distance 95%: {train_overall.get('hd95', 0):.4f}")
        logger.log(f"  📈 分类性能指标:")
        logger.log(f"    精确率 (Precision): {train_overall.get('precision', 0):.6f}")
        logger.log(f"    召回率 (Recall): {train_overall.get('recall', 0):.6f}")
        logger.log(f"    F1-Score: {train_overall.get('f1', 0):.6f}")
        logger.log(f"    特异性 (Specificity): {train_overall.get('specificity', 0):.6f}")
        
        if train_per_class:
            logger.log(f"\n📊 按类别详细指标 (训练集1/8采样估算):")
            for cls_name in CLASSES:
                if cls_name in train_per_class:
                    cls_metrics = train_per_class[cls_name]
                    logger.log(f"  📌 {cls_name}: Dice={cls_metrics.get('dice', 0):.6f}, IoU={cls_metrics.get('iou', 0):.6f}, F1={cls_metrics.get('f1', 0):.6f}")
                else:
                    logger.log(f"  📌 {cls_name}: 无数据")
        
        logger.log(f"="*100)
        
        # 使用集成的MONAI指标系统进行额外计算
        if MONAI_AVAILABLE and dice_metric is not None:
            # 重置指标
            dice_metric.reset()
            iou_metric.reset()
            if hd_metric is not None:
                hd_metric.reset()
            for metric in confusion_matrix_metrics.values():
                metric.reset()
        
        # 简化的epoch总结（避免与详细报告重复）
        logger.log(f"\n📋 Epoch {epoch+1}/{args.num_epochs} 简要总结:")
        logger.log(f"  🔥 损失: 训练={avg_train_loss:.6f} (1/8采样估算) | 测试={avg_test_loss:.6f} (完整数据)")
        logger.log(f"  📈 学习率: {optimizer.param_groups[0]['lr']:.2e}")
        logger.log(f"  🎯 核心指标: 训练Dice={train_aggregated['overall'].get('dice', 0):.4f} (1/8采样估算) | 测试Dice={test_aggregated['overall'].get('dice', 0):.4f} (完整数据)")
        
        # 表现最佳类别分析（保留这个有用的信息）
        test_summary = test_aggregated.get('summary', {})
        if test_summary.get('top_performing_class'):
            logger.log(f"  🏆 最佳表现:")
            top_performers = test_summary['top_performing_class']
            dice_best = top_performers.get('dice', {})
            iou_best = top_performers.get('iou', {})
            if dice_best.get('class') and dice_best.get('value', 0) > 0:
                logger.log(f"    Dice最佳: {dice_best['class']} ({dice_best['value']:.4f})")
            if iou_best.get('class') and iou_best.get('value', 0) > 0:
                logger.log(f"    IoU最佳: {iou_best['class']} ({iou_best['value']:.4f})")
        
        # 增强的TensorBoard记录（实现方案1(5)：层级结构）
        if writer is not None:
            # 损失记录
            writer.add_scalar('Loss/Train_Total', avg_train_loss, epoch)
            writer.add_scalar('Loss/Test_Total', avg_test_loss, epoch)
            writer.add_scalar('Training/Learning_Rate', optimizer.param_groups[0]['lr'], epoch)
            
            # 记录不确定性权重（如果使用联合损失）
            if args.loss_type == 'combined' and hasattr(criterion, 'uncertainty_weighter'):
                try:
                    uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
                    if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
                        loss_weights_info = uncertainty_weighter.get_loss_weights_info()
                        writer.add_scalar('Uncertainty/DiceFocal_Sigma', loss_weights_info['dice_focal_uncertainty'], epoch)
                        writer.add_scalar('Uncertainty/AUC_Sigma', loss_weights_info['auc_uncertainty'], epoch)
                        writer.add_scalar('Uncertainty/DiceFocal_Weight', loss_weights_info['dice_focal_weight'], epoch)
                        writer.add_scalar('Uncertainty/AUC_Weight', loss_weights_info['auc_weight'], epoch)
                        
                        # 记录权重比例
                        weight_ratio = loss_weights_info['auc_weight'] / loss_weights_info['dice_focal_weight'] if loss_weights_info['dice_focal_weight'] > 0 else 0
                        writer.add_scalar('Uncertainty/Weight_Ratio_AUC_vs_DiceFocal', weight_ratio, epoch)
                        
                        # ★ 新增：记录分项加权损失
                        # 从训练指标中提取损失分解信息
                        total_dice_weighted = 0
                        total_auc_weighted = 0
                        total_dice_raw = 0
                        total_auc_raw = 0
                        total_samples = 0
                        
                        for box_metrics in train_per_box_metrics:
                            if box_metrics.get('loss_breakdown'):
                                breakdown = box_metrics['loss_breakdown']
                                if 'dice_focal_weighted' in breakdown:
                                    total_dice_weighted += breakdown['dice_focal_weighted']
                                    total_samples += 1
                                if 'auc_weighted' in breakdown:
                                    total_auc_weighted += breakdown['auc_weighted']
                                if 'dice_focal_raw' in breakdown:
                                    total_dice_raw += breakdown['dice_focal_raw']
                                if 'auc_raw' in breakdown:
                                    total_auc_raw += breakdown['auc_raw']
                        
                        if total_samples > 0:
                            avg_dice_weighted = total_dice_weighted / total_samples
                            avg_auc_weighted = total_auc_weighted / total_samples
                            avg_dice_raw = total_dice_raw / total_samples
                            avg_auc_raw = total_auc_raw / total_samples
                            
                            writer.add_scalar('WeightedLoss/DiceFocal_Weighted', avg_dice_weighted, epoch)
                            writer.add_scalar('WeightedLoss/AUC_Weighted', avg_auc_weighted, epoch)
                            writer.add_scalar('WeightedLoss/Total_Weighted', avg_dice_weighted + avg_auc_weighted, epoch)
                            writer.add_scalar('WeightedLoss/DiceFocal_Raw', avg_dice_raw, epoch)
                            writer.add_scalar('WeightedLoss/AUC_Raw', avg_auc_raw, epoch)
                except Exception as e:
                    logger.log(f"警告: 无法记录不确定性权重到TensorBoard: {e}")
                    pass
            
            # ★ 新增：记录可学习阈值（如果使用STE）
            if ste_module is not None:
                try:
                    thresholds = ste_module.get_thresholds()
                    if ste_module.num_classes > 1:
                        for i, cls_name in enumerate(CLASSES):
                            if i < len(thresholds):
                                writer.add_scalar(f'STE_Thresholds/{cls_name}', thresholds[i].item(), epoch)
                    else:
                        writer.add_scalar('STE_Thresholds/Global', thresholds[0].item(), epoch)
                except Exception as e:
                    logger.log(f"警告: 无法记录可学习阈值到TensorBoard: {e}")
            
            # 记录分别的损失统计（如果有的话）
            if loss_aggregator is not None and hasattr(loss_aggregator, 'get_detailed_stats'):
                auc_stats = loss_aggregator.get_detailed_stats()
                total_auc_loss = auc_stats.get('total_avg_loss', 0)
                writer.add_scalar('Loss/Train_AUC', total_auc_loss, epoch)
                
                for cls_name, avg_loss in auc_stats.get('per_class_avg_loss', {}).items():
                    writer.add_scalar(f'Loss/Train_AUC_{cls_name}', avg_loss, epoch)
            
            # ========== 层级结构指标记录 ==========
            
            # 1. 整体指标 (Metrics/Overall/)
            train_overall = train_aggregated['overall']
            test_overall = test_aggregated['overall']
            
            # 基础分割指标
            writer.add_scalar('Metrics/Overall/Train_Dice', train_overall.get('dice', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Dice', test_overall.get('dice', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_IoU', train_overall.get('iou', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_IoU', test_overall.get('iou', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_Jaccard', train_overall.get('jaccard', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Jaccard', test_overall.get('jaccard', 0), epoch)
            
            # 边界指标
            writer.add_scalar('Metrics/Overall/Train_Surface_Dice', train_overall.get('surface_dice', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Surface_Dice', test_overall.get('surface_dice', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_Boundary_IoU', train_overall.get('boundary_iou', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Boundary_IoU', test_overall.get('boundary_iou', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_HD95', train_overall.get('hd95', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_HD95', test_overall.get('hd95', 0), epoch)
            
            # 分类性能指标
            writer.add_scalar('Metrics/Overall/Train_Precision', train_overall.get('precision', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Precision', test_overall.get('precision', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_Recall', train_overall.get('recall', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Recall', test_overall.get('recall', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_F1', train_overall.get('f1', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_F1', test_overall.get('f1', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_Specificity', train_overall.get('specificity', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Specificity', test_overall.get('specificity', 0), epoch)
            
            # 准确率指标
            writer.add_scalar('Metrics/Overall/Train_Accuracy', train_overall.get('accuracy', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Accuracy', test_overall.get('accuracy', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_Balanced_Accuracy', train_overall.get('balanced_accuracy', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_Balanced_Accuracy', test_overall.get('balanced_accuracy', 0), epoch)
            
            # 错误率指标
            writer.add_scalar('Metrics/Overall/Train_FPR', train_overall.get('fpr', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_FPR', test_overall.get('fpr', 0), epoch)
            writer.add_scalar('Metrics/Overall/Train_FNR', train_overall.get('fnr', 0), epoch)
            writer.add_scalar('Metrics/Overall/Test_FNR', test_overall.get('fnr', 0), epoch)
            
            # 2. 按类别指标 (Metrics/Per_Class/)
            train_per_class = train_aggregated.get('per_class', {})
            test_per_class = test_aggregated.get('per_class', {})
            
            for cls_name in CLASSES:
                if cls_name in train_per_class and cls_name in test_per_class:
                    train_cls = train_per_class[cls_name]
                    test_cls = test_per_class[cls_name]
                    
                    # 基础分割指标
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Dice', train_cls.get('dice', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Dice', test_cls.get('dice', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_IoU', train_cls.get('iou', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_IoU', test_cls.get('iou', 0), epoch)
                    
                    # 边界指标
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Surface_Dice', train_cls.get('surface_dice', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Surface_Dice', test_cls.get('surface_dice', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Boundary_IoU', train_cls.get('boundary_iou', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Boundary_IoU', test_cls.get('boundary_iou', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_HD95', train_cls.get('hd95', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_HD95', test_cls.get('hd95', 0), epoch)
                    
                    # 分类性能指标
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Precision', train_cls.get('precision', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Precision', test_cls.get('precision', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Recall', train_cls.get('recall', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Recall', test_cls.get('recall', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_F1', train_cls.get('f1', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_F1', test_cls.get('f1', 0), epoch)
                    
                    # 准确率和特异性
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Accuracy', train_cls.get('accuracy', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Accuracy', test_cls.get('accuracy', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Train_Specificity', train_cls.get('specificity', 0), epoch)
                    writer.add_scalar(f'Metrics/Per_Class/{cls_name}/Test_Specificity', test_cls.get('specificity', 0), epoch)
            
            # 3. 数据统计记录
            if test_summary := test_aggregated.get('summary'):
                writer.add_scalar('Statistics/Total_Boxes', test_summary.get('total_boxes', 0), epoch)
                boxes_per_class = test_summary.get('boxes_per_class', {})
                for cls_name, count in boxes_per_class.items():
                    writer.add_scalar(f'Statistics/Boxes_Per_Class/{cls_name}', count, epoch)
        

        
        # 保存最佳模型（基于测试Dice）
        current_test_dice = test_aggregated['overall'].get('dice', 0)
        if current_test_dice > best_test_dice:
            best_test_dice = current_test_dice
            checkpoint = {
                'epoch': epoch + 1,
                'sam_state_dict': sam.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'test_loss': avg_test_loss,
                'test_dice': current_test_dice,
                'train_metrics': train_aggregated,
                'test_metrics': test_aggregated,
                'args': vars(args) 
            }
            
            # 保存WTEnhancer状态（如果使用）
            if args.use_wt_enhancer and wt_enhancer:
                checkpoint['wt_enhancer_state_dict'] = wt_enhancer.state_dict()
                checkpoint['wt_enhancer_args'] = {
                    'levels': args.wt_enhancer_levels,
                    'wavelet': args.wt_enhancer_wavelet,
                    'kernel_size': args.wt_enhancer_kernel_size,
                }
                
            torch.save(checkpoint, os.path.join(args.output_dir, 'best_model.pth'))
            logger.log(f"  ✅ 保存最佳模型 (测试Dice: {best_test_dice:.4f})")
        
        # 每5个epoch周期性保存
        if (epoch + 1) % 5 == 0:
            checkpoint = {
                'epoch': epoch + 1,
                'sam_state_dict': sam.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'test_loss': avg_test_loss,
                'test_dice': current_test_dice,
                'train_metrics': train_aggregated,
                'test_metrics': test_aggregated,
                'args': vars(args) 
            }
            
            # 保存WTEnhancer状态（如果使用）
            if args.use_wt_enhancer and wt_enhancer:
                checkpoint['wt_enhancer_state_dict'] = wt_enhancer.state_dict()
                checkpoint['wt_enhancer_args'] = {
                    'levels': args.wt_enhancer_levels,
                    'wavelet': args.wt_enhancer_wavelet,
                    'kernel_size': args.wt_enhancer_kernel_size,
                }
                
            torch.save(checkpoint, os.path.join(args.output_dir, f'checkpoint_epoch_{epoch+1}.pth'))
            logger.log(f"  💾 周期性保存检查点: epoch_{epoch+1}")
        
    # 清理
    if writer is not None:
        writer.close()
    logger.log("按框训练完成!")
    
    logger.log("🎉 按框指标训练完成！")

# ==================== 单框二分类AUC损失实现 ====================
class PerBoxBinaryAUCLoss(nn.Module):
    """
    单框二分类AUC损失
    基于AUC-Oriented-Domain-Adaptation的empirical AUC loss设计
    专门为单框训练设计，解决维度不匹配和架构不一致问题
    """
    def __init__(self, 
                 epsilon=0.05, 
                 loss_type='log_loss',
                 max_samples_per_box=2000,
                 sampling_strategy='random',
                 class_weights=None,
                 debug=False):
        super().__init__()
        self.epsilon = epsilon
        self.loss_func = _get_auc_loss_function(loss_type)
        self.max_samples_per_box = max_samples_per_box
        self.sampling_strategy = sampling_strategy
        self.class_weights = class_weights
        self.debug = debug
    
    def forward(self, pred_probs, target_mask, class_id=None, box_id=None):
        """
        Args:
            pred_probs: [B, H, W] or [H, W] - 前景预测概率 (经过sigmoid)
            target_mask: [B, H, W] or [H, W] - 二元目标掩码
            class_id: int or list/tensor of ints - 类别ID
            box_id: int or list/tensor of ints - 框ID
        
        Returns:
            loss: scalar tensor or tensor of shape [B] - AUC损失值
        """
        # 统一输入为批量形式
        if pred_probs.dim() == 2:
            pred_probs = pred_probs.unsqueeze(0)
            target_mask = target_mask.unsqueeze(0)
            class_id = [class_id] if class_id is not None else [None]
        
        batch_size = pred_probs.shape[0]
        losses = []
        
        for i in range(batch_size):
            single_pred_probs = pred_probs[i]
            single_target_mask = target_mask[i]
            single_class_id = class_id[i] if class_id and i < len(class_id) else None
            single_box_id = box_id[i] if box_id and i < len(box_id) else None

            loss = self._compute_empirical_auc_loss(single_pred_probs, single_target_mask, single_class_id, single_box_id)
            
            # 应用类别权重
            if self.class_weights is not None and single_class_id is not None and single_class_id < len(self.class_weights):
                weight = self.class_weights[single_class_id].to(loss.device)
            loss = loss * weight
            
            losses.append(loss)
        
        return torch.stack(losses)
    
    def _compute_empirical_auc_loss(self, pred_probs, target_mask, class_id, box_id):
        """
        基于AUC-Oriented-Domain-Adaptation的empirical error计算
        """
        # 1. 展平为像素级预测和目标
        pred_flat = pred_probs.view(-1)  # [H*W]
        target_flat = target_mask.view(-1)  # [H*W]
        
        # 2. 分离正负样本
        pos_mask = (target_flat == 1)
        neg_mask = (target_flat == 0)
        
        pos_indices = pos_mask.nonzero(as_tuple=False).squeeze(-1)
        neg_indices = neg_mask.nonzero(as_tuple=False).squeeze(-1)
        
        N_pos = pos_indices.shape[0]
        N_neg = neg_indices.shape[0]
        
        if self.debug and box_id is not None and box_id % 100 == 0:
            print(f"[PerBoxAUC] Box {box_id} (class {class_id}): N_pos={N_pos}, N_neg={N_neg}")
        
        # 🚀 处理边界情况 - 回退到BCE损失而不是返回0
        if N_pos == 0 or N_neg == 0:
            if self.debug and box_id is not None and box_id % 100 == 0:
                print(f"[PerBoxAUC] Box {box_id}: 样本不足，回退到BCE损失")
            # 回退到BCE损失，确保梯度不会断开
            bce_loss = F.binary_cross_entropy(pred_flat.clamp(1e-7, 1-1e-7), target_flat, reduction='mean')
            return bce_loss
        
        # 4. 样本采样（避免内存爆炸）
        pos_indices, neg_indices = self._sample_indices(pos_indices, neg_indices)
        
        # 5. 获取正负样本预测值
        pred_pos = pred_flat[pos_indices]  # [N_pos_sampled]
        pred_neg = pred_flat[neg_indices]  # [N_neg_sampled]
        
        # 6. 计算所有正负样本对的差值 (核心AUC计算)
        delta_f_pos_negs = pred_pos.view(-1, 1) - pred_neg.view(1, -1)  # [N_pos, N_neg]
        
        # 7. 应用AUC损失函数 (参考原始实现)
        empirical_x = 4.0 * (1.0 - delta_f_pos_negs)  # [N_pos, N_neg]
        
        # 8. 计算损失并归一化
        loss_matrix = self.loss_func(empirical_x, self.epsilon)  # [N_pos, N_neg]
        total_loss = loss_matrix.sum()
        
        # 9. 归一化因子 (基于实际正负样本对数量)
        normalization_factor = 1.0 / (len(pos_indices) * len(neg_indices))
        normalized_loss = total_loss * normalization_factor
        
        if self.debug and box_id is not None and box_id % 100 == 0:
            print(f"[PerBoxAUC] Box {box_id} loss: {normalized_loss.item():.4f}")
        
        return normalized_loss
    
    def _sample_indices(self, pos_indices, neg_indices):
        """
        智能采样策略，平衡计算效率和性能
        """
        N_pos, N_neg = len(pos_indices), len(neg_indices)
        
        # 计算最大可处理的正负样本对数
        max_pairs = self.max_samples_per_box * self.max_samples_per_box
        max_pos = min(N_pos, int(np.sqrt(max_pairs)))
        max_neg = min(N_neg, max_pairs // max_pos)
        
        if self.sampling_strategy == 'random':
            # 随机采样
            if N_pos > max_pos:
                pos_perm = torch.randperm(N_pos, device=pos_indices.device)[:max_pos]
                pos_indices = pos_indices[pos_perm]
            if N_neg > max_neg:
                neg_perm = torch.randperm(N_neg, device=neg_indices.device)[:max_neg]
                neg_indices = neg_indices[neg_perm]
                
        elif self.sampling_strategy == 'hard_mining':
            # 困难样本挖掘（未来扩展）
            pass
        
        return pos_indices, neg_indices


class ClassAwareBoxAggregator(nn.Module):
    """
    类别感知的框级别损失聚合器
    """
    def __init__(self, classes_list, class_weights=None, aggregation_method='weighted_mean'):
        super().__init__()
        self.classes_list = classes_list
        self.num_classes = len(classes_list)
        self.class_weights = class_weights
        self.aggregation_method = aggregation_method
        self.box_losses = []  # 存储当前batch的所有框损失
        
    def accumulate_box_loss(self, loss, class_id, box_id):
        """累积单个框的损失"""
        self.box_losses.append({
            'loss': loss,
            'class_id': class_id,
            'class_name': self.classes_list[class_id] if class_id < len(self.classes_list) else 'unknown',
            'box_id': box_id
        })
    
    def aggregate_and_reset(self):
        """聚合并重置累积的损失"""
        if not self.box_losses:
            return torch.tensor(0.0, requires_grad=True)
        
        # 按类别分组
        losses_by_class = {}
        for box_data in self.box_losses:
            class_name = box_data['class_name']
            if class_name not in losses_by_class:
                losses_by_class[class_name] = []
            losses_by_class[class_name].append(box_data['loss'])
        
        # 计算加权聚合损失
        device = self.box_losses[0]['loss'].device if self.box_losses else 'cpu'
        total_loss = torch.tensor(0.0, device=device, requires_grad=True)
        
        for class_idx, class_name in enumerate(self.classes_list):
            if class_name in losses_by_class:
                class_losses = torch.stack(losses_by_class[class_name])
                class_mean_loss = class_losses.mean()
                
                # 应用类别权重
                if self.class_weights is not None and class_idx < len(self.class_weights):
                    weight = self.class_weights[class_idx].to(device)
                    class_mean_loss = class_mean_loss * weight
                
                total_loss = total_loss + class_mean_loss
        
        # 归一化（按实际有损失的类别数）
        num_classes_with_loss = len(losses_by_class)
        if num_classes_with_loss > 0:
            total_loss = total_loss / num_classes_with_loss
        
        # 重置累积
        self.box_losses = []
        return total_loss
    
    def get_detailed_stats(self):
        """获取详细的统计信息（用于日志记录）"""
        if not self.box_losses:
            return {}
        
        stats = {
            'total_boxes': len(self.box_losses),
            'per_class_boxes': {},
            'per_class_avg_loss': {}
        }
        
        # 按类别统计
        losses_by_class = {}
        for box_data in self.box_losses:
            class_name = box_data['class_name']
            if class_name not in losses_by_class:
                losses_by_class[class_name] = []
            losses_by_class[class_name].append(box_data['loss'].item())
        
        for class_name, losses in losses_by_class.items():
            stats['per_class_boxes'][class_name] = len(losses)
            stats['per_class_avg_loss'][class_name] = np.mean(losses)
        
        return stats

def convert_class_indices_to_multilabel(class_indices_tensor, num_classes):
    """
    将类别索引张量转换为多标签二元掩码格式
    
    Args:
        class_indices_tensor: (B, H, W) - 类别索引，值为0,1,2,3等
        num_classes: 类别总数
    
    Returns:
        multilabel_tensor: (B, C, H, W) - 多标签二元掩码，每个类别独立
    """
    B, H, W = class_indices_tensor.shape
    multilabel_tensor = torch.zeros(B, num_classes, H, W, device=class_indices_tensor.device)
    
    for class_idx in range(num_classes):
        multilabel_tensor[:, class_idx, :, :] = (class_indices_tensor == class_idx).float()
    
    return multilabel_tensor

def process_batch_boxes_parallel(embeddings_list, target_masks_list, fidt_maps_list, bboxes_list,
                               class_names_list, class_ids_list, box_ids_list,
                               sam, wt_enhancer, sam_transform, args, device):
    """
    🚀 优化的批量处理：真正的外部批量调度，解决SAM维度问题和内存管理
    
    核心改进：
    1. 完全绕过SAM内部批处理逻辑
    2. 实现外部批量调度和结果聚合
    3. 增强内存管理和错误处理
    4. 添加详细的调试信息
    """
    batch_size = len(embeddings_list)
    if batch_size == 0:
        return []
    
    # 🔧 强制清理GPU缓存
    torch.cuda.empty_cache()
    
    # 🔍 增强的调试信息
    print(f"\n🔍 批处理调试信息 (v2.0 - 外部批量调度):")
    print(f"  📊 批次大小: {batch_size}")
    print(f"  📐 嵌入形状示例: {embeddings_list[0].shape}")
    print(f"  🎯 目标掩码形状示例: {target_masks_list[0].shape}")
    print(f"  💾 GPU内存使用 (开始): {torch.cuda.memory_allocated()/1024**3:.2f}GB")
    print(f"  🔧 处理策略: 外部批量调度 (绕过SAM内部批处理)")
    
    total_start = time.time()
    
    # 🚀 按尺寸分组（保持现有逻辑）
    size_groups = {}
    for i, (embedding, mask) in enumerate(zip(embeddings_list, target_masks_list)):
        # 创建基于嵌入和掩码尺寸的组键
        embed_size = tuple(embedding.shape)
        mask_size = tuple(mask.shape)
        group_key = f"{embed_size}_{mask_size}"
        
        if group_key not in size_groups:
            size_groups[group_key] = []
        size_groups[group_key].append(i)
    
    print(f"  🗂️  尺寸分组: {len(size_groups)} 个组")
    for group_key, indices in size_groups.items():
        print(f"    - {group_key}: {len(indices)} 个框")
    
    all_pred_masks: List[Optional[torch.Tensor]] = [None]*batch_size  # 保持原始顺序
    
    # 对每个尺寸组进行外部批量调度处理
    for group_idx, (group_key, indices) in enumerate(size_groups.items()):
        group_size = len(indices)
        print(f"\n  🔄 处理组 {group_idx+1}/{len(size_groups)}: {group_size} 个框")
        
        try:
            # 收集组内数据
            group_embeddings = [embeddings_list[i] for i in indices]
            group_target_masks = [target_masks_list[i] for i in indices]
            group_fidt_maps = [fidt_maps_list[i] for i in indices]  # 新增：收集FIDT图
            group_bboxes = [bboxes_list[i] for i in indices]
            
            # 🌊 小波增强预处理
            enhance_start = time.time()
            if args.use_wt_enhancer and wt_enhancer:
                # 逐个应用小波增强，避免批量处理中的维度问题
                enhanced_embeddings = []
                for emb in group_embeddings:
                    # 确保正确的4D格式：[1, C, H, W]
                    if emb.dim() == 3:  # [C, H, W]
                        emb = emb.unsqueeze(0)  # [1, C, H, W]
                    enhanced_emb = wt_enhancer(emb)
                    enhanced_embeddings.append(enhanced_emb)
                
                # 堆叠增强后的嵌入
                stacked_embeddings = torch.cat(enhanced_embeddings, dim=0)  # [group_size, C, H, W]
                
                # 清理中间变量
                del enhanced_embeddings
                torch.cuda.empty_cache()
            else:
                # 直接堆叠原始嵌入
                normalized_embeddings = []
                for emb in group_embeddings:
                    if emb.dim() == 3:  # [C, H, W]
                        emb = emb.unsqueeze(0)  # [1, C, H, W]
                    normalized_embeddings.append(emb)
                
                stacked_embeddings = torch.cat(normalized_embeddings, dim=0)  # [group_size, C, H, W]
                del normalized_embeddings
            
            print(f"    ✅ 成功堆叠嵌入: {stacked_embeddings.shape}")
            print(f"    🌊 小波增强耗时: {time.time() - enhance_start:.3f}s")
            
            # 🚀 使用优化的真正批量化处理
            group_pred_masks = process_same_size_group_batch_optimized(
                stacked_embeddings, group_bboxes, group_target_masks, group_fidt_maps,
                sam, sam_transform, device, debug=(group_size <= 10), args=args  # 小组启用调试
            )
            
            # 将结果放回原始位置
            for pred_mask, orig_idx in zip(group_pred_masks, indices):
                all_pred_masks[orig_idx] = pred_mask
            
            # 🔧 清理中间变量
            del stacked_embeddings, group_pred_masks
            torch.cuda.empty_cache()
            
        except Exception as group_error:
            print(f"    ❌ 组处理失败: {group_error}")
            print(f"    🔍 详细错误: {traceback.format_exc()}")
            
            # 回退到逐个处理
            print(f"    🔄 回退到逐个处理...")
            for local_idx, orig_idx in enumerate(indices):
                single_start = time.time()
                
                try:
                    embedding = group_embeddings[local_idx]
                    target_mask = group_target_masks[local_idx] 
                    bbox = group_bboxes[local_idx]
                    
                    # 小波增强（单个）
                    current_embedding = embedding
                    if args.use_wt_enhancer and wt_enhancer:
                        if current_embedding.dim() == 3:
                            current_embedding = current_embedding.unsqueeze(0)
                        current_embedding = wt_enhancer(current_embedding)
                    
                    # 单框推理
                    original_size = (target_mask.shape[-2], target_mask.shape[-1])
                    pred_mask = process_single_box_optimized(
                        current_embedding, target_mask, bbox, sam, sam_transform,
                        original_size, device
                    )
                    
                    all_pred_masks[orig_idx] = pred_mask
                    
                    if local_idx % 10 == 0:
                        print(f"      🔄 单框{local_idx+1}耗时: {time.time() - single_start:.3f}s")
                    
                    # 🔧 清理单框处理的中间变量
                    del current_embedding, pred_mask
                    
                except Exception as single_error:
                    print(f"      ❌ 单框{local_idx+1}处理失败: {single_error}")
                    # 创建零掩码作为回退
                    original_size = (target_masks_list[orig_idx].shape[-2], target_masks_list[orig_idx].shape[-1])
                    zero_mask = torch.zeros(original_size, device=device)
                    
                    # 安全地将零掩码分配到结果列表
                    try:
                        # 确保列表足够长
                        while len(all_pred_masks) <= orig_idx:
                            all_pred_masks.append(None)
                        # 类型安全的赋值
                        if isinstance(all_pred_masks, list):
                            all_pred_masks[orig_idx] = zero_mask.detach().clone()
                        else:
                            print(f"      ⚠️ all_pred_masks不是列表类型: {type(all_pred_masks)}")
                    except (IndexError, TypeError, AttributeError) as assign_error:
                        print(f"      ⚠️ 无法设置索引{orig_idx}的掩码: {assign_error}")
                        # 如果无法赋值，创建新的掩码
                        if orig_idx < len(all_pred_masks):
                            all_pred_masks[orig_idx] = torch.zeros_like(zero_mask)
                    continue
        
        # 🔧 组间强制清理
        if group_idx % 2 == 0:  # 每两组清理一次
            torch.cuda.empty_cache()
    
    total_time = time.time() - total_start
    print(f"\n  ⏱️  总批处理耗时: {total_time:.3f}s")
    print(f"  ⚡ 平均每框耗时: {total_time/batch_size:.3f}s")
    print(f"  💾 GPU内存使用 (结束): {torch.cuda.memory_allocated()/1024**3:.2f}GB")
    print(f"  🎯 成功处理框数: {sum(1 for mask in all_pred_masks if mask is not None)}/{batch_size}")
    
    # 🔧 最终清理
    torch.cuda.empty_cache()
    
    return all_pred_masks

def compute_batch_loss_parallel(pred_masks_list, target_masks_list, metadata_list,
                                args, criterion, loss_aggregator, device, compute_metrics=True, ste_module=None):
    """
    🚀 真正的并行损失计算 - 重写实现批量损失计算优化
    
    Args:
        pred_masks_list: 预测掩码列表
        target_masks_list: 目标掩码列表
        metadata_list: 元数据列表（包含class_name, class_id, box_id）
        compute_metrics: 是否计算详细指标（训练时设为False以提升速度）
        其他参数同单框处理
    
    Returns:
        tuple: (总损失, 框指标列表)
    """
    batch_size = len(pred_masks_list)
    
    # 🔍 详细调试信息
    print(f"\n🔍 损失计算调试信息:")
    print(f"  📊 损失计算批次大小: {batch_size}")
    print(f"  🎯 损失类型: {args.loss_type}")
    print(f"  💾 GPU内存使用 (损失开始): {torch.cuda.memory_allocated()/1024**3:.2f}GB")
    
    loss_start_time = time.time()
    total_loss = torch.tensor(0.0, device=device, requires_grad=True)
    batch_box_metrics = []
    detailed_loss_breakdown = {}
    
    # 尝试将相同尺寸的掩码批量处理
    # 1. 按相同尺寸对框进行分组
    size_groups = {}
    for i, (pred_mask, target_mask) in enumerate(zip(pred_masks_list, target_masks_list)):
        size = (pred_mask.shape[-2], pred_mask.shape[-1])
        if size not in size_groups:
            size_groups[size] = []
        size_groups[size].append((i, pred_mask, target_mask))
    
    # 2. 逐个处理每个尺寸组
    group_idx = 0
    num_groups = len(size_groups)
    
    batch_idx_placeholder = -1 # 用于日志记录

    for size, items in size_groups.items():
        group_idx += 1
        
        # 批量处理相同尺寸的掩码
        indices = [item[0] for item in items]
        pred_masks_batch = torch.stack([item[1] for item in items], dim=0)  # [B_group, H, W]
        target_masks_batch = torch.stack([item[2] for item in items], dim=0)  # [B_group, H, W]
        
        # 批量损失计算（传递compute_metrics参数和ste_module）
        group_loss, group_metrics, group_loss_breakdown = compute_same_size_loss_batch(
            pred_masks_batch, target_masks_batch, indices, metadata_list,
            args, criterion, device, batch_idx_placeholder, group_idx, num_groups, compute_metrics, ste_module
        )

        total_loss = total_loss + group_loss
        batch_box_metrics.extend(group_metrics)
        # 累积每个组的损失分解信息
        for k, v in group_loss_breakdown.items():
            if k not in detailed_loss_breakdown:
                detailed_loss_breakdown[k] = []
            detailed_loss_breakdown[k].extend(v)

    # 3. 聚合详细的损失分解信息 (确保CUDA张量正确转换)
    final_loss_breakdown = {}
    for k, v in detailed_loss_breakdown.items():
        if v:
            # 确保所有值都是Python标量，避免CUDA张量转换错误
            cpu_values = []
            for item in v:
                if hasattr(item, 'cpu') and hasattr(item, 'item'):
                    # GPU张量，先转CPU再提取值
                    cpu_values.append(item.cpu().item())
                elif hasattr(item, 'item'):
                    # CPU张量，直接提取值
                    cpu_values.append(item.item())
                else:
                    # 已经是Python标量
                    cpu_values.append(float(item))
            final_loss_breakdown[k] = np.mean(cpu_values)
        else:
            final_loss_breakdown[k] = 0.0

    return total_loss, batch_box_metrics, final_loss_breakdown


def compute_same_size_loss_batch(pred_masks_batch, target_masks_batch, indices, metadata_list,
                                args, criterion, device, batch_idx, group_idx, num_groups, compute_metrics=True, ste_module=None):
    """
    批量计算相同尺寸掩码的损失
    """
    group_start_time = time.time()
    batch_size = pred_masks_batch.shape[0]
    group_loss = torch.tensor(0.0, device=device, requires_grad=True)
    group_metrics = []
    
    # 用于聚合整个组的损失分解信息
    group_loss_breakdown_agg = {
        'dice_focal_raw': [], 'auc_raw': [],
        'dice_focal_norm': [], 'auc_norm': [],
        'dice_focal_weighted': [], 'auc_weighted': []
    }

    if args.debug:
        print(f"  🔄 损失组 {group_idx}/{num_groups}: {batch_size} 个框 (尺寸: {'x'.join(map(str, pred_masks_batch.shape[-2:]))})")
    
    # 🚀 关键修复：生成bbox_masks批次，限制损失计算在框内
    bbox_masks_batch = []
    for i, idx in enumerate(indices):
        bbox = metadata_list[idx]['bbox']
        bbox_mask = create_bbox_mask(
            mask_shape=pred_masks_batch[i].shape, 
            bbox=bbox, 
            device=device
        )
        bbox_masks_batch.append(bbox_mask)
    
    bbox_masks_batch = torch.stack(bbox_masks_batch, dim=0)  # [B, H, W]
    
    if args.debug:
        print(f"    🎯 生成bbox_masks: {bbox_masks_batch.shape}")
        # 计算bbox内有效像素比例
        bbox_coverage = torch.mean(bbox_masks_batch).item()
        print(f"    📊 bbox覆盖率: {bbox_coverage:.1%} (有效像素占比)")
    
    # 🚀 应用bbox_mask：只在框内计算损失和指标
    pred_masks_roi = pred_masks_batch * bbox_masks_batch
    target_masks_roi = target_masks_batch * bbox_masks_batch
    
    # 为联合损失准备4D张量
    pred_masks_4d = pred_masks_roi.unsqueeze(1)  # [B, 1, H, W]
    target_masks_4d = target_masks_roi.unsqueeze(1)  # [B, 1, H, W]
    pred_probs_batch = torch.sigmoid(pred_masks_roi)  # [B, H, W]
    
    # 批量计算联合损失
    # 注意：这里的循环是为了逐个获取class_id和box_id，但损失计算本身是批量的
    combined_loss, dice_focal_raw, auc_raw, dice_focal_norm, auc_norm, _hf_loss = criterion(
        pred_masks_4d, pred_probs_batch, target_masks_4d, 
        [metadata_list[i]['class_id'] for i in indices],
        [metadata_list[i]['box_id'] for i in indices]
    )
    group_loss = combined_loss # 不再求平均，因为criterion返回的是标量

    # 获取当前权重
    current_weights_info = criterion.uncertainty_weighter.get_loss_weights_info()
    dice_weight = current_weights_info['dice_focal_weight']  # DiceFocal权重
    auc_weight = current_weights_info['auc_weight']   # AUC权重

    # 记录详细的损失分解
    group_loss_breakdown_agg['dice_focal_raw'].extend(safe_extract_tensor_items(dice_focal_raw, batch_size))
    group_loss_breakdown_agg['auc_raw'].extend(safe_extract_tensor_items(auc_raw, batch_size))
    group_loss_breakdown_agg['dice_focal_norm'].extend(safe_extract_tensor_items(dice_focal_norm, batch_size))
    group_loss_breakdown_agg['auc_norm'].extend(safe_extract_tensor_items(auc_norm, batch_size))
    
    # 计算加权后的损失贡献
    dice_focal_weighted = dice_focal_norm * dice_weight
    auc_weighted = auc_norm * auc_weight
    group_loss_breakdown_agg['dice_focal_weighted'].extend(safe_extract_tensor_items(dice_focal_weighted, batch_size))
    group_loss_breakdown_agg['auc_weighted'].extend(safe_extract_tensor_items(auc_weighted, batch_size))

    # 计算指标（可选，训练时跳过以提速）
    # 修复：直接使用传入的compute_metrics参数，确保测试阶段能正常计算指标
    if args.debug:
        print(f"    🔍 是否计算指标: {compute_metrics}")
    
    if compute_metrics:
        with torch.no_grad():
            for i in range(batch_size):
                metadata = metadata_list[indices[i]]
                
                # 🔍 关键debug：检查输入张量状态（使用ROI）
                if args.debug:
                    pred_sum = torch.sum(pred_probs_batch[i] > 0.5).item()
                    target_sum = torch.sum(target_masks_roi[i] > 0.5).item()
                    bbox_pixels = torch.sum(bbox_masks_batch[i]).item()
                    print(f"    🔍 框 {i+1}/{batch_size} (类别:{metadata['class_name']}, ID:{metadata['box_id']})")
                    print(f"      pred_shape: {pred_probs_batch[i].shape}, target_shape: {target_masks_roi[i].shape}")
                    print(f"      🎯 bbox内像素: {bbox_pixels}, pred_pixels: {pred_sum}, target_pixels: {target_sum}")
                
                try:
                    # 🚀 关键修复：使用ROI数据计算指标，避免背景稀释；使用STE
                    box_metrics = calculate_per_box_metrics(
                        pred_probs_batch[i], target_masks_roi[i], metadata['class_id'],
                        debug=args.debug, ste_module=ste_module
                    )
                    
                    # 🔍 验证返回的指标
                    if args.debug:
                        if box_metrics is None:
                            print(f"      ❌ calculate_per_box_metrics 返回了 None!")
                        elif not isinstance(box_metrics, dict):
                            print(f"      ❌ calculate_per_box_metrics 返回类型错误: {type(box_metrics)}")
                        else:
                            print(f"      ✅ 指标计算成功: dice={box_metrics.get('dice', 'N/A'):.4f}")
                    
                    group_metrics.append({
                        'class_name': metadata['class_name'], 'box_id': metadata['box_id'],
                        'metrics': box_metrics
                    })
                    
                except Exception as e:
                    if args.debug:
                        print(f"      ❌ 指标计算失败: {e}")
                    # 即使失败也要添加一个占位条目
                    group_metrics.append({
                        'class_name': metadata['class_name'], 'box_id': metadata['box_id'],
                        'metrics': None
                    })

    if args.debug:
        # 安全计算平均值，避免CUDA张量转换错误
        def safe_mean(values):
            if not values:
                return 0.0
            cpu_values = [v.cpu().item() if hasattr(v, 'cpu') else (v.item() if hasattr(v, 'item') else float(v)) for v in values]
            return np.mean(cpu_values)
        
        print(f"    📊 损失分解 (平均值):")
        print(f"       - 原始损失: DiceFocal={safe_mean(group_loss_breakdown_agg['dice_focal_raw']):.4f}, AUC={safe_mean(group_loss_breakdown_agg['auc_raw']):.4f}")
        print(f"       - 对数归一化后: DiceFocal={safe_mean(group_loss_breakdown_agg['dice_focal_norm']):.4f}, AUC={safe_mean(group_loss_breakdown_agg['auc_norm']):.4f}")
        print(f"       - 加权后贡献: DiceFocal={safe_mean(group_loss_breakdown_agg['dice_focal_weighted']):.4f}, AUC={safe_mean(group_loss_breakdown_agg['auc_weighted']):.4f}")
        print(f"    ⚖️  当前权重: DiceFocal={dice_weight:.4f}, AUC={auc_weight:.4f}")
        print(f"    🎯 最终组损失 (平均): {group_loss.item():.6f}")
        print(f"    ⏱️  组处理耗时: {time.time() - group_start_time:.3f}s")
        
    return group_loss, group_metrics, group_loss_breakdown_agg


def process_single_box_optimized(embedding, target_mask, bbox, sam, sam_transform,
                               original_size, device):
    """
    优化的单框处理（减少不必要的维度操作）
    🚀 修改：使用点提示模式替代边界框提示
    """
    # 🚀 修改：计算边界框中心点并使用点提示
    bbox_sam = bbox[0].float() if bbox.dim() > 1 else bbox.float()

    # 计算中心点坐标
    x1, y1, x2, y2 = bbox_sam[0], bbox_sam[1], bbox_sam[2], bbox_sam[3]
    center_x = (x1 + x2) / 2.0
    center_y = (y1 + y2) / 2.0
    center_point = torch.tensor([[center_x.item(), center_y.item()]], dtype=torch.float)

    # 应用坐标变换
    point_transformed = sam_transform.apply_coords(center_point.numpy(), original_size)
    point_coords = torch.as_tensor(point_transformed, dtype=torch.float, device=device).unsqueeze(0)  # [1, 1, 2]
    point_labels = torch.ones(1, 1, device=device, dtype=torch.int)  # [1, 1] 前景点

    # 提示编码
    with torch.no_grad():
        sparse_embeddings, dense_embeddings = sam.prompt_encoder(
            points=(point_coords, point_labels),
            boxes=None,
            masks=None,
        )

    # 掩码解码
    low_res_masks, iou_predictions = sam.mask_decoder(
        image_embeddings=embedding,
        image_pe=sam.prompt_encoder.get_dense_pe(),
        sparse_prompt_embeddings=sparse_embeddings,
        dense_prompt_embeddings=dense_embeddings,
        multimask_output=False,
    )
    
    # 上采样
    pred_logits = F.interpolate(
        low_res_masks,
        original_size,
        mode="bilinear",
        align_corners=False,
    )  # 保持logits形式，不立即sigmoid
    
    # ★ 新增: 小波增强移到解码阶段
    # 如果有小波增强器，对logits进行增强
    if hasattr(sam, 'wt_enhancer') and sam.wt_enhancer is not None:
        pred_logits = sam.wt_enhancer(pred_logits)
    
    # 最终sigmoid得到预测掩码
    pred_mask = torch.sigmoid(pred_logits)[0, 0]  # 移除batch和channel维度
    
    return pred_mask


def compute_single_box_loss(pred_mask, target_mask, metadata, args, criterion, device, compute_metrics=True, ste_module=None):
    """
    计算单个框的损失和指标
    
    Args:
        compute_metrics: 是否计算详细指标（训练时设为False以提升速度）
    """
    with torch.cuda.amp.autocast(enabled=args.use_amp):
        class_id = metadata['class_id']
        class_name = metadata['class_name']
        box_id = metadata['box_id']
        bbox = metadata['bbox']
        
        # 创建bbox_mask
        bbox_mask = create_bbox_mask(target_mask.shape, bbox, device)
        
        if args.loss_type == 'combined':
            # 联合损失
            pred_probs = torch.sigmoid(pred_mask)
            target_mask_2d = target_mask.squeeze()
            
            pred_mask_4d = pred_mask.unsqueeze(0).unsqueeze(0)
            target_mask_4d = target_mask_2d.unsqueeze(0).unsqueeze(0)
            
            combined_loss, dice_focal_loss_raw, auc_loss_raw_batch, dice_focal_norm, auc_norm, _hf_loss = criterion(
                pred_mask_4d * bbox_mask, pred_probs * bbox_mask, target_mask_4d * bbox_mask, class_id, box_id
            )
            loss = combined_loss
            
            current_weights = criterion.uncertainty_weighter.get_loss_weights_info()
            loss_breakdown = {
                'dice_focal_loss_raw': dice_focal_loss_raw.item(),
                'auc_loss_raw'      : auc_loss_raw_batch.mean().item(),
                'dice_focal_loss_norm': dice_focal_norm.item(),
                'auc_loss_norm'       : auc_norm.item(),
                'dice_focal_weight'   : current_weights['dice_focal_weight'],
                'auc_weight'          : current_weights['auc_weight']
            }
        elif hasattr(criterion, 'forward') and 'class_id' in criterion.forward.__code__.co_varnames:
            # AUC损失
            pred_probs = torch.sigmoid(pred_mask)
            target_mask_2d = target_mask.squeeze()
            loss = criterion(pred_probs * bbox_mask, target_mask_2d * bbox_mask, class_id, box_id)
            loss_breakdown = {}
        else:
            # DiceFocal或BCE损失
            pred_mask_4d = pred_mask.unsqueeze(0).unsqueeze(0)
            target_mask_4d = target_mask.squeeze().unsqueeze(0).unsqueeze(0)
            loss = criterion(pred_mask_4d * bbox_mask, target_mask_4d * bbox_mask)
            loss_breakdown = {}
    
    # 条件性计算指标
    with torch.no_grad():
        if compute_metrics:
            pred_prob = torch.sigmoid(pred_mask)
            box_metrics = calculate_per_box_metrics(
                pred_prob * bbox_mask, target_mask * bbox_mask, class_id,
                debug=args.debug if hasattr(args, 'debug') else False, ste_module=ste_module
            )
        else:
            box_metrics = None  # 训练时不计算详细指标
        
        box_metrics_dict = {
            'class_name': class_name,
            'box_id': box_id,
            'metrics': box_metrics,
            'loss_breakdown': loss_breakdown
        }
    
    # 只在必要时清理缓存，减少开销
    if box_id % 100 == 0:  # 每100个框清理一次
        torch.cuda.empty_cache()
    
    return loss, box_metrics_dict


def check_system_dependencies():
    """
    检查系统依赖和配置，提供详细的诊断信息
    """
    import torch  # 确保torch在函数内可用
    
    print("🔍 系统依赖检查:")
    
    # 检查PyTorch CUDA支持
    print(f"  ✅ PyTorch版本: {torch.__version__}")
    print(f"  {'✅' if torch.cuda.is_available() else '❌'} CUDA可用性: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        try:
            import torch.version
            if hasattr(torch.version, 'cuda') and torch.version.cuda:
                cuda_version = torch.version.cuda
            else:
                cuda_version = 'N/A'
            print(f"  ✅ CUDA版本: {cuda_version}")
        except (AttributeError, ImportError):
            print(f"  ✅ CUDA版本: N/A")
        print(f"  ✅ 检测到GPU数量: {torch.cuda.device_count()}")
        print(f"  ✅ 当前GPU: {torch.cuda.current_device()}")
        print(f"  ✅ GPU名称: {torch.cuda.get_device_name()}")
        print(f"  ✅ GPU计算能力: {torch.cuda.get_device_capability()}")
        
        # 检查GPU内存
        total_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        print(f"  ✅ GPU总内存: {total_memory:.1f}GB")
    
    # 检查可选依赖
    print("  🔍 可选依赖检查:")
    
    try:
        import pynvml
        pynvml.nvmlInit()
        print("  ✅ pynvml: 已安装且可用")
        
        # 尝试获取GPU利用率
        handle = pynvml.nvmlDeviceGetHandleByIndex(0)
        util = pynvml.nvmlDeviceGetUtilizationRates(handle)
        print(f"    - GPU利用率: {util.gpu}%")
        print(f"    - 显存利用率: {util.memory}%")
        
    except ImportError:
        print("  ⚠️  pynvml: 未安装 (GPU利用率监控将不可用)")
        print("    安装命令: pip install pynvml")
        print("    或: conda install -c conda-forge pynvml")
    except Exception as e:
        print(f"  ⚠️  pynvml: 安装但初始化失败: {e}")
    
    try:
        import tensorboard
        print(f"  ✅ TensorBoard: {tensorboard.__version__}")
    except ImportError:
        print("  ⚠️  TensorBoard: 未安装")
    
    try:
        import monai
        print(f"  ✅ MONAI: {monai.__version__}")
    except ImportError:
        print("  ⚠️  MONAI: 未安装")
    
    print("  🔍 系统环境:")
    import os
    import sys
    print(f"    - Python路径: {sys.executable}")
    print(f"    - 工作目录: {os.getcwd()}")
    if 'CUDA_VISIBLE_DEVICES' in os.environ:
        print(f"    - CUDA_VISIBLE_DEVICES: {os.environ['CUDA_VISIBLE_DEVICES']}")
    else:
        print(f"    - CUDA_VISIBLE_DEVICES: 未设置")

def get_safe_gpu_info():
    """
    安全获取GPU状态信息，避免因缺少依赖库而报错
    
    Returns:
        dict: GPU状态信息
    """
    gpu_info = {
        'available': False,
        'memory_allocated_gb': 0.0,
        'memory_reserved_gb': 0.0,
        'utilization_percent': 'N/A',
        'device_name': 'N/A'
    }
    
    if torch.cuda.is_available():
        gpu_info['available'] = True
        
        try:
            gpu_info['memory_allocated_gb'] = torch.cuda.memory_allocated() / (1024**3)
            gpu_info['memory_reserved_gb'] = torch.cuda.memory_reserved() / (1024**3)
        except Exception as e:
            print(f"警告: 无法获取GPU内存信息: {e}")
        
        # 安全获取GPU利用率
        try:
            # 优先尝试使用pynvml（如果可用）
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            util = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_info['utilization_percent'] = f"{util.gpu}%"
        except (ImportError, Exception):
            # 回退到torch.cuda.utilization()（可能不准确）
            try:
                gpu_info['utilization_percent'] = f"{torch.cuda.utilization()}%"
            except Exception:
                gpu_info['utilization_percent'] = 'N/A (需要pynvml库)'
        
        try:
            gpu_info['device_name'] = torch.cuda.get_device_name()
        except Exception as e:
            print(f"警告: 无法获取GPU设备名称: {e}")
    
    return gpu_info

def check_and_log_uncertainty_weights_update(batch_idx, criterion, pre_update_weights, pre_update_log_vars, batch_loss_breakdown=None):
    """
    统一的不确定性权重更新检查和日志记录
    
    Args:
        batch_idx: 批次索引
        criterion: 损失函数
        pre_update_weights: 更新前的权重信息
        pre_update_log_vars: 更新前的log_vars
        batch_loss_breakdown: 批次损失分解信息
    """
    if not hasattr(criterion, 'uncertainty_weighter'):
        return
        
    try:
        uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
        if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
            post_update_weights = uncertainty_weighter.get_loss_weights_info()
            post_update_log_vars = [param.data.clone() for param in uncertainty_weighter.parameters()]
            
            # 检查是否有实际更新
            weights_changed = False
            for i, (pre, post) in enumerate(zip(pre_update_log_vars, post_update_log_vars)):
                if pre.numel() == 1 and post.numel() == 1:
                    if abs(pre.item() - post.item()) > 1e-8:
                        weights_changed = True
                        break
                else:
                    if torch.any(torch.abs(pre - post) > 1e-8):
                        weights_changed = True
                        break
            
            if weights_changed:
                print(f"    🎯 不确定性权重更新成功 (batch {batch_idx+1}):")
                for i, (pre, post) in enumerate(zip(pre_update_log_vars, post_update_log_vars)):
                    if pre.numel() == 1 and post.numel() == 1:
                        print(f"      log_var[{i}]: {pre.item():.8f} → {post.item():.8f} (Δ={post.item()-pre.item():+.8f})")
                    else:
                        # 多元素张量，显示第一个元素
                        pre_val = pre.flatten()[0].item()
                        post_val = post.flatten()[0].item()
                        print(f"      log_var[{i}][0]: {pre_val:.8f} → {post_val:.8f} (Δ={post_val-pre_val:+.8f})")
                
                if post_update_weights is not None and pre_update_weights is not None:
                    print(f"      权重: DiceFocal {pre_update_weights['dice_focal_weight']:.6f}→{post_update_weights['dice_focal_weight']:.6f}, AUC {pre_update_weights['auc_weight']:.6f}→{post_update_weights['auc_weight']:.6f}")
                
                # 📊 新增：详细损失分解日志
                if batch_loss_breakdown is not None:
                    print(f"    📊 详细损失分解:")
                    df_raw = batch_loss_breakdown.get('dice_focal_raw', 0)
                    auc_raw = batch_loss_breakdown.get('auc_raw', 0)
                    df_norm = batch_loss_breakdown.get('dice_focal_norm', 0)
                    auc_norm = batch_loss_breakdown.get('auc_norm', 0)
                    df_weighted = batch_loss_breakdown.get('dice_focal_weighted', 0)
                    auc_weighted = batch_loss_breakdown.get('auc_weighted', 0)
                    
                    print(f"      🎯 DiceFocal损失:")
                    print(f"        - 原始值: {df_raw:.6f}")
                    print(f"        - 对数归一化后: {df_norm:.6f}")
                    print(f"        - 不确定性加权: {df_weighted:.6f} (权重×{post_update_weights['dice_focal_weight']:.4f})")
                    
                    print(f"      🎯 AUC损失:")
                    print(f"        - 原始值: {auc_raw:.6f}")
                    print(f"        - 对数归一化后: {auc_norm:.6f}")
                    print(f"        - 不确定性加权: {auc_weighted:.6f} (权重×{post_update_weights['auc_weight']:.4f})")
                    
                    total_weighted = df_weighted + auc_weighted
                    df_contribution = (df_weighted / total_weighted * 100) if total_weighted > 0 else 0
                    auc_contribution = (auc_weighted / total_weighted * 100) if total_weighted > 0 else 0
                    
                    print(f"      📈 损失贡献比例:")
                    print(f"        - DiceFocal: {df_contribution:.1f}%")
                    print(f"        - AUC: {auc_contribution:.1f}%")
                    
            else:
                print(f"    ⚠️ 不确定性权重未更新 (batch {batch_idx+1}) - 检查梯度和学习率")
                # 检查梯度
                for i, param in enumerate(uncertainty_weighter.parameters()):
                    if param.grad is not None:
                        if param.grad.numel() == 1:
                            print(f"      log_var[{i}] 梯度: {param.grad.item():.8f}")
                        else:
                            print(f"      log_var[{i}] 梯度: {param.grad.flatten()[0].item():.8f} (第一个元素)")
                    else:
                        print(f"      log_var[{i}] 梯度: None")
                        
    except Exception as e:
        print(f"    ❌ 权重更新检查失败: {e}")

def display_uncertainty_weights_info(epoch, args, criterion, logger):
    """
    🔍 显示不确定性权重变化情况的详细调试信息
    
    实现任务2：增加更多调试信息，在终端输出中显示不确定性权重变化情况
    """
    if args.loss_type != 'combined':
        # 只在联合损失模式下显示不确定性权重
        return
    
    try:
        # 检查criterion是否有不确定性权重信息
        uncertainty_weighter = getattr(criterion, 'uncertainty_weighter', None)
        if uncertainty_weighter and hasattr(uncertainty_weighter, 'get_loss_weights_info'):
            # 获取不确定性权重信息
            loss_weights_info = uncertainty_weighter.get_loss_weights_info()
            
            print(f"\n🔍 === 不确定性权重详细信息 (Epoch {epoch+1}) ===")
            print(f"⚖️  学习到的权重:")
            print(f"   🎯 DiceFocal损失权重: {loss_weights_info['dice_focal_weight']:.6f}")
            print(f"   🎯 AUC损失权重: {loss_weights_info['auc_weight']:.6f}")
            print(f"   📊 权重比例 (AUC/DiceFocal): {loss_weights_info['auc_weight']/loss_weights_info['dice_focal_weight']:.4f}")
            
            print(f"🌊 不确定性参数 (σ):")
            print(f"   📈 DiceFocal不确定性 (σ₁): {loss_weights_info['dice_focal_uncertainty']:.6f}")
            print(f"   📈 AUC不确定性 (σ₂): {loss_weights_info['auc_uncertainty']:.6f}")
            
            # 权重变化趋势分析
            weight_ratio = loss_weights_info['auc_weight'] / loss_weights_info['dice_focal_weight']
            if weight_ratio > 1.2:
                trend = "🔺 AUC损失占主导地位"
                explanation = "模型认为AUC优化更重要"
            elif weight_ratio < 0.8:
                trend = "🔻 DiceFocal损失占主导地位"
                explanation = "模型认为形状精度更重要"
            else:
                trend = "⚖️  两个损失基本平衡"
                explanation = "模型认为两个任务同等重要"
            
            print(f"💭 权重分析: {trend}")
            print(f"   解释: {explanation}")
            
            # 不确定性变化趋势
            uncertainty_ratio = loss_weights_info['dice_focal_uncertainty'] / loss_weights_info['auc_uncertainty']
            if uncertainty_ratio > 1.1:
                uncertainty_trend = "DiceFocal任务不确定性更高"
                uncertainty_explanation = "模型对形状预测不够自信"
            elif uncertainty_ratio < 0.9:
                uncertainty_trend = "AUC任务不确定性更高"  
                uncertainty_explanation = "模型对排序预测不够自信"
            else:
                uncertainty_trend = "两个任务不确定性相当"
                uncertainty_explanation = "模型对两个任务都有相似的自信度"
            
            print(f"🔮 不确定性分析: {uncertainty_trend}")
            print(f"   解释: {uncertainty_explanation}")
            
            # 记录到日志
            logger.log(f"⚖️  Epoch {epoch+1} 不确定性权重: DiceFocal={loss_weights_info['dice_focal_weight']:.4f}, AUC={loss_weights_info['auc_weight']:.4f}, 比例={weight_ratio:.3f}")
            logger.log(f"🌊 Epoch {epoch+1} 不确定性σ: DiceFocal={loss_weights_info['dice_focal_uncertainty']:.4f}, AUC={loss_weights_info['auc_uncertainty']:.4f}")
            logger.log(f"💭 Epoch {epoch+1} 权重趋势: {trend} - {explanation}")
            
            print(f"=" * 80)
            
        elif hasattr(criterion, 'get_loss_weights_info'):
            # 旧版本的实现，直接调用criterion的方法
            loss_weights_info = criterion.get_loss_weights_info()
            
            print(f"\n🔍 === 不确定性权重信息 (Epoch {epoch+1}) ===")
            print(f"⚖️  DiceFocal权重: {loss_weights_info['dice_focal_weight']:.6f}")
            print(f"⚖️  AUC权重: {loss_weights_info['auc_weight']:.6f}")
            print(f"🌊 DiceFocal不确定性: {loss_weights_info['dice_focal_uncertainty']:.6f}")
            print(f"🌊 AUC不确定性: {loss_weights_info['auc_uncertainty']:.6f}")
            
            logger.log(f"⚖️  Epoch {epoch+1} 权重: DiceFocal={loss_weights_info['dice_focal_weight']:.4f}, AUC={loss_weights_info['auc_weight']:.4f}")
            print(f"=" * 60)
        else:
            print(f"\n⚠️  Epoch {epoch+1}: 联合损失模式但无法获取不确定性权重信息")
            logger.log(f"⚠️  Epoch {epoch+1}: 无法获取不确定性权重信息")
            
    except Exception as e:
        print(f"\n❌ Epoch {epoch+1}: 显示不确定性权重信息时出错: {e}")
        logger.log(f"❌ Epoch {epoch+1}: 显示不确定性权重信息失败: {e}")
        import traceback
        logger.log(f"详细错误: {traceback.format_exc()}")


def safe_extract_tensor_items(tensor_data, num_items):
    """
    安全地从张量或列表中提取单个项目，确保CUDA张量正确转换
    
    Args:
        tensor_data: 可能是张量、列表或单个值
        num_items: 期望的项目数量
    
    Returns:
        list: 包含Python标量值的列表
    """
    def extract_scalar(item):
        """安全提取标量值，处理CUDA张量"""
        if hasattr(item, 'cpu') and hasattr(item, 'item'):
            # GPU张量，先转CPU再提取
            return item.cpu().item()
        elif hasattr(item, 'item'):
            # CPU张量，直接提取
            return item.item()
        else:
            # 已经是Python标量
            return float(item)
    
    try:
        if isinstance(tensor_data, list):
            # 如果已经是列表，逐个安全提取
            return [extract_scalar(item) for item in tensor_data]
        elif hasattr(tensor_data, 'shape') and len(tensor_data.shape) > 0:
            # 如果是多维张量，按元素提取
            if tensor_data.numel() == 1:
                # 单元素张量，重复num_items次
                scalar_val = extract_scalar(tensor_data)
                return [scalar_val] * num_items
            else:
                # 多元素张量，逐个提取
                result = []
                for i in range(min(tensor_data.numel(), num_items)):
                    result.append(extract_scalar(tensor_data[i]))
                return result
        else:
            # 标量值，重复num_items次
            scalar_val = extract_scalar(tensor_data)
            return [scalar_val] * num_items
    except Exception as e:
        print(f"警告: 张量项目提取失败: {e}, 使用默认值")
        return [0.0] * num_items

# ==================== 真正的批量化处理函数 ====================

def process_batch_with_safe_sequential(sam, image_embeddings_batch, boxes_batch, target_masks_list, fidt_maps_list, original_size, device, debug=False, is_training=True):
    """
    🚀 安全的批量化处理 - 使用优化的顺序处理避免SAM内部维度问题
    
    由于SAM内部实现的限制，采用优化的顺序处理策略：
    1. 减少GPU kernel启动开销
    2. 优化内存访问模式
    3. 保持数据在GPU上连续处理
    
    Args:
        sam: SAM模型实例
        image_embeddings_batch: 批量化的图像嵌入 [B, C, H_emb, W_emb]
        boxes_batch: 批量化的边界框 [B, 4]
        original_size: 原始图像尺寸 (H, W)
        device: 计算设备
        debug: 是否启用调试信息
    
    Returns:
        list: 预测掩码列表
    """
    batch_size = image_embeddings_batch.shape[0]
    
    if debug:
        print(f"\n🚀 安全批量化处理开始:")
        print(f"  📊 批次大小: {batch_size}")
        print(f"  📐 图像嵌入形状: {image_embeddings_batch.shape}")
        print(f"  📦 边界框形状: {boxes_batch.shape}")
        print(f"  🎯 目标输出尺寸: {original_size}")
    
    # 🚀 新增：尝试真正的并行批处理
    try:
        return process_batch_with_true_parallel(sam, image_embeddings_batch, boxes_batch, target_masks_list, fidt_maps_list, original_size, device, debug, is_training)
    except Exception as parallel_error:
        if debug:
            print(f"  ⚠️  真正并行处理失败: {parallel_error}")
            print(f"  🔄 回退到优化的顺序处理...")
    
    # 预分配结果列表
    pred_masks_list = []
    total_time = 0
    
    # 🔥 优化的顺序处理：减少GPU同步开销
    for i in range(batch_size):
        iter_start = time.time()
        
        # 获取单个样本数据
        single_embedding = image_embeddings_batch[i:i+1]  # 保持batch维度 [1, C, H, W]
        single_box = boxes_batch[i:i+1]  # [1, 4]
        
        if debug and (i == 0 or i % 10 == 0):
            print(f"    🔄 处理框 {i+1}/{batch_size}")
            print(f"      - 嵌入形状: {single_embedding.shape}")
            print(f"      - 边界框形状: {single_box.shape}")
        
        with torch.no_grad():
            # 🚀 修改：步骤1: 使用动态点提示采样
            # 获取当前样本的FIDT图和目标掩码
            fidt_map = fidt_maps_list[i]  # [H, W]
            target_mask = target_masks_list[i]  # [H, W]

            # 使用动态点提示采样
            point_coords, point_labels = dynamic_point_sampling(
                fidt_map=fidt_map,
                target_mask=target_mask,
                is_training=is_training,
                device=device
            )

            # 转换为SAM期望的格式 [1, N, 2] 和 [1, N]
            point_coords_sam = point_coords.unsqueeze(0)  # [1, N, 2]
            point_labels_sam = point_labels.unsqueeze(0)  # [1, N]

            sparse_embeddings, dense_embeddings = sam.prompt_encoder(
                points=(point_coords_sam, point_labels_sam),
                boxes=None,
                masks=None
            )
            
            if debug and i == 0:
                print(f"      - sparse_embeddings: {sparse_embeddings.shape}")
                print(f"      - dense_embeddings: {dense_embeddings.shape}")
            
            # 步骤2: 掩码解码
            low_res_masks, iou_predictions = sam.mask_decoder(
                image_embeddings=single_embedding,
                image_pe=sam.prompt_encoder.get_dense_pe(),
                sparse_prompt_embeddings=sparse_embeddings,
                dense_prompt_embeddings=dense_embeddings,
                multimask_output=False,
            )
            
            if debug and i == 0:
                print(f"      - low_res_masks: {low_res_masks.shape}")
                print(f"      - iou_predictions: {iou_predictions.shape}")
            
            # 步骤3: 上采样
            upscaled_mask = F.interpolate(
                low_res_masks,
                size=original_size,
                mode="bilinear",
                align_corners=False,
            )[0, 0]  # 移除batch和channel维度 -> [H, W]
            
            pred_masks_list.append(upscaled_mask)
        
        iter_time = time.time() - iter_start
        total_time += iter_time
        
        if debug and (i == 0 or (i + 1) % 10 == 0):
            print(f"      ⏱️  框 {i+1} 处理耗时: {iter_time:.3f}s")
    
    avg_time = total_time / batch_size
    
    if debug:
        print(f"  ✅ 安全批量化处理完成")
        print(f"  ⏱️  总耗时: {total_time:.3f}s")
        print(f"  ⚡ 平均每框耗时: {avg_time:.3f}s")
        print(f"  🎯 处理成功: {len(pred_masks_list)}/{batch_size} 个框")
        print(f"  📊 相比原始逐个处理的优化: 连续GPU处理，减少同步开销")
    
    return pred_masks_list


def process_batch_with_true_parallel(sam, image_embeddings_batch, boxes_batch, target_masks_list, fidt_maps_list, original_size, device, debug=False, is_training=True):
    """
    🚀 真正的并行批处理实现 - 提升GPU利用率
    
    核心思想：
    1. 批量编码所有提示
    2. 并行调用mask_decoder
    3. 批量上采样所有结果
    
    这样可以最大化GPU并行计算，提升利用率
    """
    batch_size = image_embeddings_batch.shape[0]
    
    if debug:
        print(f"\n🚀 真正并行批处理开始:")
        print(f"  📊 批次大小: {batch_size}")
        print(f"  🎯 目标: 最大化GPU并行计算")
    
    # 🔥 步骤1: 批量提示编码
    prompt_start = time.time()

    # 🚀 修改：使用动态点提示采样
    all_sparse_embeddings = []
    all_dense_embeddings = []

    for i in range(batch_size):
        # 获取当前样本的FIDT图和目标掩码
        fidt_map = fidt_maps_list[i]  # [H, W]
        target_mask = target_masks_list[i]  # [H, W]

        # 使用动态点提示采样
        point_coords, point_labels = dynamic_point_sampling(
            fidt_map=fidt_map,
            target_mask=target_mask,
            is_training=is_training,
            device=device
        )

        # 转换为SAM期望的格式 [1, N, 2] 和 [1, N]
        single_point_coords = point_coords.unsqueeze(0)  # [1, N, 2]
        single_point_labels = point_labels.unsqueeze(0)  # [1, N]

        sparse_emb, dense_emb = sam.prompt_encoder(
            points=(single_point_coords, single_point_labels),
            boxes=None,
            masks=None
        )
        all_sparse_embeddings.append(sparse_emb)
        all_dense_embeddings.append(dense_emb)
    
    # 堆叠成批量格式
    batch_sparse_embeddings = torch.cat(all_sparse_embeddings, dim=0)  # [B, 2, 256]
    batch_dense_embeddings = torch.cat(all_dense_embeddings, dim=0)   # [B, 256, 64, 64]
    
    prompt_time = time.time() - prompt_start
    
    if debug:
        print(f"  ✅ 批量提示编码完成: {prompt_time:.3f}s")
        print(f"    - sparse_embeddings: {batch_sparse_embeddings.shape}")
        print(f"    - dense_embeddings: {batch_dense_embeddings.shape}")
    
    # 🔥 步骤2: 真正的批量掩码解码
    decode_start = time.time()
    
    # 获取位置编码 - 对所有图像使用相同的PE
    image_pe = sam.prompt_encoder.get_dense_pe()  # [1, 256, 64, 64]
    
    # 🚀 关键：一次性调用mask_decoder处理整个批次
    try:
        batch_low_res_masks, batch_iou_predictions = sam.mask_decoder(
            image_embeddings=image_embeddings_batch,    # [B, 256, 64, 64]
            image_pe=image_pe.expand(batch_size, -1, -1, -1),  # [B, 256, 64, 64]
            sparse_prompt_embeddings=batch_sparse_embeddings,  # [B, 2, 256]
            dense_prompt_embeddings=batch_dense_embeddings,    # [B, 256, 64, 64]
            multimask_output=False,
        )
        
        decode_time = time.time() - decode_start
        
        if debug:
            print(f"  ✅ 批量掩码解码成功: {decode_time:.3f}s")
            print(f"    - low_res_masks: {batch_low_res_masks.shape}")
            print(f"    - iou_predictions: {batch_iou_predictions.shape}")
        
    except Exception as decode_error:
        if debug:
            print(f"  ❌ 批量掩码解码失败: {decode_error}")
        raise decode_error
    
    # 🔥 步骤3: 批量上采样
    upsample_start = time.time()
    
    # 批量上采样所有掩码
    batch_upsampled_masks = F.interpolate(
        batch_low_res_masks,  # [B, 1, low_H, low_W]
        size=original_size,
        mode="bilinear",
        align_corners=False,
    )  # [B, 1, H, W]
    
    # 移除channel维度并分离为列表
    pred_masks_list = [batch_upsampled_masks[i, 0] for i in range(batch_size)]
    
    upsample_time = time.time() - upsample_start
    total_time = prompt_time + decode_time + upsample_time
    
    if debug:
        print(f"  ✅ 批量上采样完成: {upsample_time:.3f}s")
        print(f"  🚀 真正并行批处理完成!")
        print(f"  ⏱️  时间分布:")
        print(f"    - 提示编码: {prompt_time:.3f}s ({prompt_time/total_time*100:.1f}%)")
        print(f"    - 掩码解码: {decode_time:.3f}s ({decode_time/total_time*100:.1f}%)")
        print(f"    - 批量上采样: {upsample_time:.3f}s ({upsample_time/total_time*100:.1f}%)")
        print(f"    - 总计: {total_time:.3f}s")
        print(f"  ⚡ 平均每框耗时: {total_time/batch_size:.3f}s")
        print(f"  🎯 GPU利用率提升: 理论上可达到更高的并行度")
    
    return pred_masks_list


def process_same_size_group_batch_optimized(stacked_embeddings, bboxes_list, target_masks_list, fidt_maps_list,
                                           sam, sam_transform, device, debug=False, args=None):
    """
    🚀 优化的同尺寸组批量处理 - 使用真正的批量化调用
    
    替换原有的逐个处理循环，实现真正的GPU并行计算
    """
    batch_size = len(bboxes_list)
    if batch_size == 0:
        return []
    
    if debug:
        print(f"\n🔧 开始优化的批量处理 {batch_size} 个框")
    
    # 获取原始尺寸（假设同组内尺寸相同）
    original_size = (target_masks_list[0].shape[-2], target_masks_list[0].shape[-1])
    
    # 🚀 批量预处理所有边界框
    bbox_start = time.time()
    transformed_boxes = []
    
    for bbox in bboxes_list:
        # 确保bbox是单个框：[4,] 或 [1, 4]
        if bbox.dim() > 1:
            bbox_single = bbox[0]  # 取第一个框
        else:
            bbox_single = bbox
        

        
        # 应用SAM变换
        bbox_transformed = sam_transform.apply_boxes(
            bbox_single.cpu().numpy().reshape(1, -1), 
            original_size
        )
        transformed_boxes.append(torch.as_tensor(bbox_transformed[0], dtype=torch.float, device=device))
    
    # 堆叠所有变换后的边界框
    stacked_boxes = torch.stack(transformed_boxes, dim=0)  # [B, 4]
    
    bbox_time = time.time() - bbox_start
    
    if debug:
        print(f"  📦 批量bbox预处理耗时: {bbox_time:.3f}s")
        print(f"  📐 堆叠后的嵌入形状: {stacked_embeddings.shape}")
        print(f"  📦 堆叠后的bbox形状: {stacked_boxes.shape}")
    
    # 🚀 调用真正的批量化处理函数
    inference_start = time.time()
    
    try:
        pred_masks_list = process_batch_with_safe_sequential(
            sam=sam,
            image_embeddings_batch=stacked_embeddings,  # [B, 256, 64, 64]
            boxes_batch=stacked_boxes,                  # [B, 4]
            target_masks_list=target_masks_list,        # [B, H, W] 新增：目标掩码
            fidt_maps_list=fidt_maps_list,              # [B, H, W] 新增：FIDT图
            original_size=original_size,                # (H, W)
            device=device,
            debug=debug and batch_size <= 10,          # 小批次启用详细调试
            is_training=args.is_training_mode if args else True  # 新增：训练模式标志
        )
        
        inference_time = time.time() - inference_start
        
        if debug:
            print(f"  ✅ 安全批量推理成功! 总耗时: {inference_time:.3f}s")
            print(f"  ⚡ 优化的顺序处理，减少GPU同步开销")
            print(f"  🎯 返回 {len(pred_masks_list)} 个预测掩码")
        
        return pred_masks_list
        
    except Exception as batch_error:
        print(f"  ❌ 批量处理失败: {batch_error}")
        print(f"  🔄 回退到逐个处理...")
        import traceback
        print(f"  详细错误: {traceback.format_exc()}")
        
        # 回退到原有的逐个处理逻辑
        pred_masks_list = []
        for i in range(batch_size):
            try:
                single_embedding = stacked_embeddings[i:i+1]  # [1, 256, 64, 64]
                single_bbox = stacked_boxes[i]  # [4]
                
                pred_mask = process_single_box_optimized(
                    single_embedding, target_masks_list[i], single_bbox, 
                    sam, sam_transform, original_size, device
                )
                pred_masks_list.append(pred_mask)
                
            except Exception as single_error:
                print(f"    ❌ 单框{i+1}处理失败: {single_error}")
                # 创建零掩码作为回退
                zero_mask = torch.zeros(original_size, device=device)
                pred_masks_list.append(zero_mask)
        
        return pred_masks_list


if __name__ == '__main__':
    args = parse_args()
    
    # 使用按框指标训练方法（新的统一方法）
    print("🚀 使用按框指标训练方法...")
    train_with_per_box_metrics(args)

