#!/usr/bin/env python3
"""
测试脚本：验证修改后的训练脚本能否正确加载和处理聚合数据
"""

import os
import torch
import numpy as np
import argparse
from torch.utils.data import DataLoader

# 导入相关模块
import sys
sys.path.append('/datadisk/SAM')

def test_data_loading():
    """
    测试数据加载功能
    """
    print("🧪 开始测试聚合数据加载...")
    
    try:
        from larch_dataset import get_larch_dataloader, AggregatedPerBoxDataset
        from train_larch_sam import aggregated_collate_fn
        print("✅ 成功导入数据加载模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试参数
    data_root = '/datadisk/FIDT-SAM/processed_larch_dataset'
    
    if not os.path.exists(data_root):
        print(f"❌ 数据根目录不存在: {data_root}")
        return False
    
    try:
        # 创建数据加载器
        print("📊 创建训练数据加载器...")
        train_loader = get_larch_dataloader(
            data_root=data_root,
            split='train',
            batch_size=2,
            shuffle=False,
            num_workers=0,
            collate_fn=aggregated_collate_fn
        )
        
        print("📊 创建测试数据加载器...")
        test_loader = get_larch_dataloader(
            data_root=data_root,
            split='test',
            batch_size=2,
            shuffle=False,
            num_workers=0,
            collate_fn=aggregated_collate_fn
        )
        
        print(f"✅ 数据加载器创建成功")
        print(f"  训练集大小: {len(train_loader.dataset)}")
        print(f"  测试集大小: {len(test_loader.dataset)}")
        
        # 测试数据批次
        print("\n🔍 测试训练数据批次...")
        for i, batch in enumerate(train_loader):
            print(f"  批次 {i+1}:")
            print(f"    embeddings: {len(batch['embeddings'])} 个")
            print(f"    masks: {len(batch['masks'])} 个")
            print(f"    fidt_maps: {len(batch['fidt_maps'])} 个")
            print(f"    bboxes: {batch['bboxes'].shape if hasattr(batch['bboxes'], 'shape') else len(batch['bboxes'])}")
            
            # 检查第一个样本的详细信息
            if len(batch['embeddings']) > 0:
                emb = batch['embeddings'][0]
                mask = batch['masks'][0]
                fidt = batch['fidt_maps'][0]
                
                print(f"    第一个样本:")
                print(f"      embedding形状: {emb.shape}")
                print(f"      mask形状: {mask.shape}")
                print(f"      fidt_map形状: {fidt.shape}")
                print(f"      mask数值范围: [{mask.min():.3f}, {mask.max():.3f}]")
                print(f"      fidt数值范围: [{fidt.min():.3f}, {fidt.max():.3f}]")
            
            if i >= 1:  # 只测试前2个批次
                break
        
        print("\n🔍 测试测试数据批次...")
        for i, batch in enumerate(test_loader):
            print(f"  批次 {i+1}:")
            print(f"    embeddings: {len(batch['embeddings'])} 个")
            print(f"    masks: {len(batch['masks'])} 个")
            print(f"    fidt_maps: {len(batch['fidt_maps'])} 个")
            
            if i >= 0:  # 只测试第1个批次
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_point_sampling_integration():
    """
    测试点提示采样与数据加载的集成
    """
    print("\n🧪 开始测试点提示采样集成...")
    
    try:
        from larch_dataset import get_larch_dataloader
        from train_larch_sam import aggregated_collate_fn, dynamic_point_sampling
        print("✅ 成功导入集成模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    data_root = '/datadisk/FIDT-SAM/processed_larch_dataset'
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 创建数据加载器
        train_loader = get_larch_dataloader(
            data_root=data_root,
            split='train',
            batch_size=1,
            shuffle=False,
            num_workers=0,
            collate_fn=aggregated_collate_fn
        )
        
        # 获取一个批次
        batch = next(iter(train_loader))
        
        # 提取数据
        mask = batch['masks'][0].to(device)
        fidt_map = batch['fidt_maps'][0].to(device)
        
        print(f"📊 测试样本信息:")
        print(f"  mask形状: {mask.shape}")
        print(f"  fidt_map形状: {fidt_map.shape}")
        
        # 测试训练模式采样
        print("🎯 测试训练模式点提示采样...")
        train_coords, train_labels = dynamic_point_sampling(
            fidt_map=fidt_map,
            target_mask=mask,
            is_training=True,
            device=device
        )
        
        print(f"  训练模式结果:")
        print(f"    点数: {len(train_coords)}")
        print(f"    正样本: {torch.sum(train_labels == 1).item()}")
        print(f"    负样本: {torch.sum(train_labels == 0).item()}")
        print(f"    坐标范围: x[{train_coords[:, 0].min():.1f}, {train_coords[:, 0].max():.1f}], y[{train_coords[:, 1].min():.1f}, {train_coords[:, 1].max():.1f}]")
        
        # 测试测试模式采样
        print("🎯 测试测试模式点提示采样...")
        test_coords, test_labels = dynamic_point_sampling(
            fidt_map=fidt_map,
            target_mask=mask,
            is_training=False,
            device=device
        )
        
        print(f"  测试模式结果:")
        print(f"    点数: {len(test_coords)}")
        print(f"    标签: {test_labels.tolist()}")
        print(f"    中心点: ({test_coords[0, 0]:.1f}, {test_coords[0, 1]:.1f})")
        
        return True
        
    except Exception as e:
        print(f"❌ 点提示采样集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sam_integration():
    """
    测试SAM模型集成（简化版本）
    """
    print("\n🧪 开始测试SAM模型集成...")
    
    try:
        from segment_anything import sam_model_registry
        print("✅ 成功导入SAM模型")
    except ImportError as e:
        print(f"❌ SAM导入失败: {e}")
        return False
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 检查SAM模型文件
        sam_checkpoint = "/datadisk/SAM/sam_vit_h_4b8939.pth"
        if not os.path.exists(sam_checkpoint):
            print(f"❌ SAM模型文件不存在: {sam_checkpoint}")
            return False
        
        print(f"📊 加载SAM模型...")
        sam = sam_model_registry["vit_h"](checkpoint=sam_checkpoint)
        sam.to(device)
        sam.eval()
        
        print(f"✅ SAM模型加载成功")
        print(f"  设备: {device}")
        print(f"  模型类型: {type(sam)}")
        
        # 测试点提示编码
        print("🎯 测试点提示编码...")
        
        # 创建测试点
        point_coords = torch.tensor([[[128.0, 128.0], [64.0, 64.0]]], device=device)  # [1, 2, 2]
        point_labels = torch.tensor([[1, 0]], device=device)  # [1, 2]
        
        with torch.no_grad():
            sparse_emb, dense_emb = sam.prompt_encoder(
                points=(point_coords, point_labels),
                boxes=None,
                masks=None
            )
        
        print(f"  点提示编码结果:")
        print(f"    sparse_embeddings: {sparse_emb.shape}")
        print(f"    dense_embeddings: {dense_emb.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ SAM模型集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    parser = argparse.ArgumentParser(description='测试训练脚本集成')
    parser.add_argument('--skip-sam', action='store_true', help='跳过SAM模型测试')
    
    args = parser.parse_args()
    
    print("🚀 开始训练脚本集成测试...\n")
    
    # 测试数据加载
    data_success = test_data_loading()
    
    # 测试点提示采样集成
    sampling_success = test_point_sampling_integration()
    
    # 测试SAM模型集成
    sam_success = True
    if not args.skip_sam:
        sam_success = test_sam_integration()
    else:
        print("\n⏭️ 跳过SAM模型测试")
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"  数据加载: {'✅ 通过' if data_success else '❌ 失败'}")
    print(f"  点提示采样集成: {'✅ 通过' if sampling_success else '❌ 失败'}")
    print(f"  SAM模型集成: {'✅ 通过' if sam_success else '❌ 失败'}")
    
    overall_success = data_success and sampling_success and sam_success
    print(f"\n🎉 总体结果: {'✅ 所有测试通过' if overall_success else '❌ 部分测试失败'}")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
