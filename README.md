# Larch Casebearer 检测与分割项目

基于SAM (Segment Anything Model) 的落叶松鞘蛾检测与分割系统，支持多种训练模式和高级功能。

## 项目概述

本项目针对落叶松小袋蛾损伤的分割任务，采用多种方法提高分割效果：

1.  **AUC优化长尾分布**: 采用AUC-Oriented方法处理类别不平衡问题。
2.  **小波变换增强**: 引入小波变换卷积（WTConv）增强多尺度分析能力：
    - 级联小波分解：对低频分量进行多级分解
    - 多尺度特征融合：分别处理各频带信息
    - 自适应特征提取：增大感受野的同时保持参数量可控

针对四类目标进行分割：

- H (Healthy): 健康区域
- LD (Light Damage): 轻度损伤
- HD (Heavy Damage): 重度损伤
- other: 其他区域

## 环境配置

项目依赖以下主要库：


## 🚀 核心功能

### 训练模式

- **标准训练**: 基于图像级别的训练
- **按框训练**: 基于每个边界框的精细训练，提供更精确的指标计算
- **多GPU并行训练**: 支持分布式训练，充分利用多GPU资源

### 高级功能

- **多种损失函数**: DiceFocal、AUC、Combined联合损失
- **小波增强**: WTEnhancedBlock小波变换增强模块
- **类别权重**: inverse、effective、none三种权重计算方法
- **混合精度训练**: 自动混合精度(AMP)支持
- **梯度优化**: 梯度裁剪、梯度累积
- **完整监控**: TensorBoard日志、MONAI指标系统

## 📦 环境配置

### 依赖安装

```bash
pip install torch torchvision
pip install segment-anything
pip install monai
pip install tensorboard
pip install opencv-python
pip install PyWavelets
pip install tqdm
pip install numpy
```

### 可选依赖

- **MONAI**: 用于高级医学图像分析指标
- **TensorBoard**: 用于训练监控和可视化
- **PyWavelets**: 用于小波增强功能

## 🎯 使用指南

### 1. 数据预处理

将原始数据转换为标准格式。脚本会自动分割训练集和测试集。

```bash
python preprocess_larch_dataset.py \
  --dataset_root /datadisk/SAM/dataset/Data_Set_Larch_Casebearer \
  --output_root /datadisk/SAM/processed_larch_dataset
```

- 这会创建 `processed_larch_dataset` 目录结构。
- 生成的 `dataset_info.json` 包含类别信息。


### 2. 生成初始伪标签

以下部分描述 `generate_initial_pseudo_labels.py` 的行为 ，该脚本现在实现了**单阶段**的伪标签生成：基于边界框提示生成掩码，然后对掩码进行可选的形态学操作（如开运算）进行优化。

```bash
# 为训练集生成单阶段伪标签并应用形态学操作
python generate_initial_pseudo_labels.py \
  --data_root /datadisk/SAM/processed_larch_dataset \
  --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --split train \
  --seed 42 \
  --num_workers 8 \
  --morph_kernel_size 3 \
  --erosion_iterations 10 \
  --dilation_iterations 10 > /datadisk/SAM/generate_train.txt 2>&1

# 为测试集生成单阶段伪标签并应用形态学操作
python generate_initial_pseudo_labels.py \
  --data_root /datadisk/SAM/processed_larch_dataset \
  --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --split test \
  --seed 42 \
  --num_workers 8 \
  --morph_kernel_size 3 \
  --erosion_iterations 10 \
  --dilation_iterations 10
```

参数说明：

- `--data_root`: 处理后的数据集根目录
- `--sam_checkpoint`: SAM 预训练模型检查点路径
- `--model_type`: SAM 模型类型 (`vit_h`, `vit_l`, `vit_b`)
- `--split`: 指定要处理的数据集分割 (`train` 或 `test`)
- `--batch_size`: 批处理大小（当前仅支持1）
- `--num_workers`: 数据加载器的工作进程数
- `--seed`: 随机种子
- `--use_wt_enhancer`: 是否启用小波变换增强模块
- `--wt_enhancer_levels`: 小波分解的级别数
- `--wt_enhancer_wavelet`: 使用的小波基函数
- `--wt_enhancer_kernel_size`: 增强模块中卷积核大小
- `--wt_enhancer_dropout`: Dropout比率
- `--morph_kernel_size`: (新增) 形态学操作（腐蚀/膨胀）的核大小，默认为3。
- `--erosion_iterations`: (新增) 腐蚀操作的迭代次数，默认为1。设置为0则不进行腐蚀。
- `--dilation_iterations`: (新增) 膨胀操作的迭代次数，默认为1。设置为0则不进行膨胀。

生成的伪标签将保存在相应的 `train_pseudo_masks` 或 `test_pseudo_masks` 目录下 
**注意**: 此脚本直接使用 SAM 模型输出的最佳二值化掩码 (基于框提示和多掩码选择)，并可进行形态学后处理。

### 3. 模型训练

训练 SAM 模型（微调 Mask Decoder）。推荐使用联合损失。

### 3.1 单GPU训练

#### 标准训练模式

```bash
python train_larch_sam.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
    --output_dir /datadisk/SAM/output \
    --num_epochs 30 \
    --lr 1e-5 \
    --loss_type dice_focal
```

#### 高级损失函数训练

```bash
# AUC损失
python train_larch_sam.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
    --output_dir /datadisk/SAM/output \
    --loss_type auc \
    --auc_loss_name log_loss \
    --auc_epsilon 0.05 \
    --weight_method inverse

# 联合损失(DiceFocal+AUC)
python train_larch_sam.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
    --output_dir /datadisk/SAM/output \
    --loss_type combined \
    --loss_alpha 0.7 \
    --auc_loss_name log_loss \
    --weight_method effective
```

#### 小波增强训练

```bash
python train_larch_sam.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
    --output_dir /datadisk/SAM/output \
    --use_wt_enhancer \
    --wt_enhancer_levels 2 \
    --wt_enhancer_wavelet bior2.2 \
    --lr_wt_enhancer 1e-4 \
    --loss_type combined
```

### 3.2 完整训练

按框训练提供更精细的指标计算，每个边界框独立训练和评估：

#### 按框训练 + 高级功能（单卡完整训练）

```bash
python train_larch_sam.py \
  --data_root /datadisk/SAM/processed_larch_dataset \
  --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --output_dir /datadisk/SAM/output \
  --loss_type combined \
  --loss_alpha 0.7 \
  --weight_method inverse \
  --auc_loss_name log_loss \
  --auc_epsilon 0.05 \
  --auc_samples 1000 \
  --auc_norm_k 0.5 \
  --dice_focal_norm_k 0.5 \
  --num_epochs 20 \
  --lr 1e-5 \
  --num_workers 12 \
  --weight_decay 0.01 \
  --batch_size 128 \
  --accumulation_steps 1 \
  --grad_clip 1.0 \
  --use_amp \
  --seed 42 \
  --use_wt_enhancer \
  --wt_enhancer_levels 2 \
  --wt_enhancer_wavelet 'bior2.2' \
  --wt_enhancer_kernel_size 3 \
  --wt_enhancer_dropout 0.1 \
  --lr_wt_enhancer 1e-4 \
  --update_interval 5 \
  --initial_pseudo_label_update_epoch 5 \
  --update_morph_kernel_size 3 \
  --update_erosion_iterations 10 \
  --update_dilation_iterations 10 > /datadisk/SAM/output/full_terminal_output.txt 2>&1
```

### 3.3 多GPU并行训练

利用多张GPU进行分布式训练，显著提升训练速度：

#### 双RTX 4090训练示例

```bash
# 使用torchrun启动分布式训练
torchrun --nproc_per_node=2 --master_port 29500 train_larch_sam.py \
    --distributed \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
    --output_dir /datadisk/SAM/output \
    --num_epochs 30 \
    --lr 1e-5 \
    --loss_type combined \
    --use_wt_enhancer
```

#### 多GPU性能说明

- **加速比**: 双卡训练通常可获得1.8-1.9倍的速度提升
- **内存效率**: 每张GPU独立处理数据，有效利用显存
- **自动同步**: DDP自动处理梯度同步和参数更新
- **容错机制**: 支持单GPU自动降级

### 4. (可选) 使用 TensorBoard 监控训练

在训练过程中，可以在另一个终端中运行 TensorBoard 来查看损失曲线和学习率：

```bash
tensorboard --logdir /datadisk/SAM/output/logs
```

然后在浏览器中访问显示的 URL (通常是 `http://localhost:6006`)。

### 5. 结果可视化

使用训练好的模型或伪标签可视化分割结果：

```bash
python visualize_masks.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --output_dir /datadisk/SAM/visualization_output \
    --split test \
    --pseudo_mask_dir_name "test/predict_masks" \
    --transparency 0.6 \
    --sample_count 20 \
    --seed 42

python visualize_masks.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --output_dir /datadisk/FIDT-SAM/visualization_output \
    --split train \
    --pseudo_mask_dir_name "train_pseudo_masks" \
    --transparency 0.6 \
    --sample_count 20 \
    --seed 42

python visualize_masks.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --output_dir /datadisk/FIDT-SAM/visualization_output \
    --split test \
    --pseudo_mask_dir_name "test_pseudo_masks" \
    --transparency 0.6 \
    --sample_count 20
    --seed 42
```

- `--pseudo_mask_dir_name`: 指定要可视化的掩码目录 (相对于 `data_root`)。可以是初始伪标签 (`test/pseudo_masks`) 或模型预测结果 (如果 `visualize_masks.py` 支持从模型生成)。
- `--seed`: 随机种子，当指定 `--sample_count` 时用于确保每次随机采样的图片一致。

## 🔧 训练脚本参数说明

### 基础参数

- `--data_root`: 处理后的数据集根目录 (默认: /datadisk/SAM/processed_larch_dataset)
- `--sam_checkpoint`: SAM预训练模型检查点路径 (默认: /datadisk/SAM/sam_vit_h_4b8939.pth)
- `--model_type`: SAM模型类型 [vit_h, vit_l, vit_b] (默认: vit_h)
- `--output_dir`: 训练输出目录 (默认: /datadisk/SAM/output)
- `--seed`: 随机种子 (默认: 42)

### 核心训练参数

- `--num_epochs`: 训练轮数 (默认: 30)
- `--batch_size`: 批次大小，按框训练建议为1 (默认: 1)
- `--lr`: 主学习率 (默认: 1e-5)
- `--weight_decay`: 权重衰减系数 (默认: 0.1)

### 损失函数参数

- `--loss_type`: 损失函数类型 [dice_focal, auc, combined] (默认: dice_focal)
- `--loss_alpha`: 联合损失中DiceFocal权重，剩余权重给AUC (默认: 0.7)
- `--weight_method`: 类别权重计算方法 [none, inverse, effective] (默认: inverse)
- `--auc_loss_name`: AUC损失类型 [log_loss, max_loss, square_loss] (默认: log_loss)
- `--auc_epsilon`: AUC损失epsilon参数 (默认: 0.05)
- `--auc_samples`: AUC损失最大采样数 (默认: 1500)
- `--auc_norm_k`: AUC损失归一化系数 (默认: 0.5)
- `--dice_focal_norm_k`: DiceFocal损失归一化系数 (默认: 0.5)

### 小波增强参数

- `--use_wt_enhancer`: 启用小波增强模块
- `--wt_enhancer_levels`: 小波分解级别数 (默认: 2)
- `--wt_enhancer_wavelet`: 小波基类型 (默认: bior2.2)
- `--wt_enhancer_kernel_size`: 小波模块内部卷积核大小 (默认: 3)
- `--wt_enhancer_dropout`: 小波模块Dropout率 (默认: 0.1)
- `--lr_wt_enhancer`: 小波模块专用学习率 (默认: 1e-4)

### 训练优化参数

- `--accumulation_steps`: 梯度累积步数 (默认: 4)
- `--grad_clip`: 梯度裁剪最大范数 (默认: 1.0)
- `--use_amp`: 启用混合精度训练 (布尔标志)

### 伪标签更新参数

- `--update_interval`: 伪标签更新间隔轮数，0或负数禁用 (默认: 5)
- `--initial_pseudo_label_update_epoch`: 首次允许伪标签更新的轮数 (默认: 10)
- `--update_morph_kernel_size`: 伪标签更新时形态学操作核大小 (默认: 3)
- `--update_erosion_iterations`: 伪标签更新时腐蚀操作迭代次数 (默认: 10)
- `--update_dilation_iterations`: 伪标签更新时膨胀操作迭代次数 (默认: 10)

### 分布式训练参数

- `--distributed`: 启用分布式多GPU训练 (布尔标志)
- `--dist_backend`: DDP后端 [nccl, gloo] (默认: nccl)
- `--dist_url`: 分布式训练初始化URL (默认: env://)

### 调试参数

- `--debug`: 启用调试模式，限制批次数量并显示详细日志 (布尔标志)

## 📊 输出说明

### 训练日志

- `training.log`: 简化训练日志
- `logs/`: TensorBoard事件文件
- `args.json`: 训练参数保存

### 模型保存

- `best_model.pth`: 基于测试Dice的最佳模型
- `checkpoint_epoch_X.pth`: 每5个epoch的检查点

### 指标记录

- **整体指标**: Dice、IoU、HD95
- **按类别指标**: 每个类别的详细指标
- **混淆矩阵**: Precision、Recall、Specificity、F1
- **TensorBoard**: 实时监控训练过程

## 🎮 高级用法示例

### 完整功能训练

```bash
# 使用所有高级功能的完整训练命令
torchrun --nproc_per_node=2 --master_port 29500 train_larch_sam.py \
  --distributed \
  --data_root /datadisk/SAM/processed_larch_dataset \
  --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --output_dir /datadisk/SAM/output \
  --loss_type combined \
  --loss_alpha 0.7 \
  --weight_method effective \
  --auc_loss_name log_loss \
  --auc_epsilon 0.005 \
  --auc_samples 2000 \
  --auc_norm_k 0.5 \
  --dice_focal_norm_k 0.5 \
  --num_epochs 30 \
  --lr 5e-5 \
  --weight_decay 0.1 \
  --batch_size 1 \
  --accumulation_steps 2 \
  --grad_clip 1.0 \
  --use_amp \
  --seed 42 \
  --use_wt_enhancer \
  --wt_enhancer_levels 2 \
  --wt_enhancer_wavelet 'bior2.2' \
  --wt_enhancer_kernel_size 3 \
  --wt_enhancer_dropout 0.1 \
  --lr_wt_enhancer 1e-4 \
  --update_interval 5 \
  --initial_pseudo_label_update_epoch 5 \
  --update_morph_kernel_size 3 \
  --update_erosion_iterations 10 \
  --update_dilation_iterations 10 > /datadisk/FIDT-SAM/output/full_terminal_output.txt 2>&1
```

### 调试模式

```bash
# 快速调试训练流程
python train_larch_sam.py \
    --debug \
    --num_epochs 2 \
    --output_dir /datadisk/SAM/debug_output
```


## 优化技术详解

本项目采用了多种优化技术来提高训练效率和稳定性：

### 梯度累积

梯度累积是一种在内存受限情况下模拟大批量训练的技术。其工作原理如下：

1. 将每个批次的损失除以累积步数 (`loss = loss / args.accumulation_steps`)
2. 对多个连续批次的梯度进行累积，而不立即更新模型参数
3. 当累积的批次数达到设定的阈值时，才执行优化器步骤更新模型参数
4. 在每个epoch结束时处理可能剩余的累积梯度

优点：

- 允许在有限内存下模拟更大的批量训练
- 可以稳定训练过程，减少梯度方差
- 对于大型模型（如SAM）特别有效

使用方法：通过 `--accumulation_steps` 参数控制，默认为4。

### 梯度裁剪

梯度裁剪是防止梯度爆炸的技术，特别是在训练复杂网络或使用较大学习率时：

1. 在每次反向传播后、优化器步骤前计算所有梯度的范数
2. 如果梯度范数超过指定阈值，则按比例缩小所有梯度
3. 这确保梯度不会变得过大，导致训练不稳定

优点：

- 防止梯度爆炸问题
- 使训练过程更加稳定
- 允许使用更大的学习率

使用方法：通过 `--grad_clip` 参数控制最大范数值，默认为1.0。

### 混合精度训练

混合精度训练使用FP16（半精度浮点数）和FP32（单精度浮点数）的组合加速训练：

1. 在前向和反向传播中使用FP16以提高计算速度并减少内存使用
2. 使用动态缩放器(scaler)防止FP16数值下溢
3. 优化器仍使用FP32更新模型权重以保持精度

优点：

- 训练速度提升（最多可达2倍）
- 显存使用减少（最多可减少一半）
- 在现代GPU上几乎没有精度损失

使用方法：使用 `--use_amp` 参数启用混合精度训练。

## 方法说明

### AUC-Oriented 长尾分布优化

本项目实现的AUC-Oriented方法特别针对类别不平衡问题，通过以下机制工作：

1.  **样本对损失**: 不直接最小化类别间隔，而是优化**类内**正负样本对之间的排序关系 (P(正样本得分 > 负样本得分))。它尝试最大化每个类别的 AUC 分数。
2.  **像素采样**: 为了计算效率，从每个类别的正样本区域和负样本区域随机采样像素点进行比较。
3.  **类别权重**: 可以使用基于类别频率的权重策略 (`inverse`, `effective`) 来调整每个类别 AUC 损失的贡献，通常用于强调稀有类别。
    *   `inverse`: 权重与类别像素数成反比。
    *   `effective`: 基于论文 "Class-Balanced Loss Based on Effective Number of Samples" 计算权重，公式为 `(1-β^n)/(1-β)`，其中 `n` 是样本数，`β` 是超参数 (通常接近1)。

## 使用训练好的模型进行推理 (predict_mask.py)

该脚本使用训练好的 SAM 模型在指定数据集分割上生成预测掩码。

### 执行命令

```bash
python predict_mask.py [参数]
```

### 参数说明

- `--data_root` (str, 默认: `/datadisk/SAM/processed_larch_dataset`): 处理后的数据集根目录。
- `--model_checkpoint` (str, 默认: `/datadisk/SAM/output/best_model.pth`): 训练好的SAM模型检查点路径。
- `--model_type` (str, 默认: `vit_h`): SAM模型类型 (必须与训练时一致)。
- `--output_dir_name` (str, 默认: `predict_masks`): 保存预测掩码的目录名 (位于 `data_root/split` 下)。
- `--batch_size` (int, 默认: `1`): 批处理大小 (当前实现只支持1)。
- `--num_workers` (int, 默认: `0`): 数据加载器的工作进程数。
- `--seed` (int, 默认: `42`): 随机种子 (主要用于可复现的数据加载顺序)。
- `--split` (str, 默认: `test`, 可选: `train`, `test`, `val`): 要进行预测的数据集分割。

### 示例

```bash
python predict_mask.py \
    --data_root /datadisk/SAM/processed_larch_dataset \
    --model_checkpoint /datadisk/SAM/your/best_model.pth \
    --output_dir_name predicted_masks \
    --split test
```



