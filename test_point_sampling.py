#!/usr/bin/env python3
"""
测试脚本：验证动态点提示采样功能
用于检查新的点提示采样算法是否正确工作
"""

import os
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
import argparse

# 导入训练脚本中的点提示采样函数
import sys
sys.path.append('/datadisk/SAM')

def generate_fidt_map(mask, alpha=0.02, beta=0.75, C=1):
    """
    生成FIDT图（从generate_initial_pseudo_labels.py复制）
    """
    # 确保掩码是二值的
    binary_mask = (mask > 0.5).astype(np.uint8)
    
    # 计算距离变换
    # 对于前景区域：计算到背景的距离
    # 对于背景区域：计算到前景的距离
    
    # 前景距离（前景像素到最近背景像素的距离）
    foreground_dist = cv2.distanceTransform(binary_mask, cv2.DIST_L2, 5)
    
    # 背景距离（背景像素到最近前景像素的距离）
    background_dist = cv2.distanceTransform(1 - binary_mask, cv2.DIST_L2, 5)
    
    # 合并距离：前景区域使用前景距离，背景区域使用背景距离
    combined_distances = np.where(binary_mask > 0, foreground_dist, background_dist)
    
    # 应用FIDT公式: I = P / (α*P + β) + C
    # 其中P是距离值
    P = combined_distances
    fidt_map = P / (alpha * P + beta) + C
    
    return fidt_map.astype(np.float32)

def create_test_mask(size=(256, 256), shape_type='circle'):
    """
    创建测试用的掩码
    """
    mask = np.zeros(size, dtype=np.uint8)
    h, w = size
    
    if shape_type == 'circle':
        # 创建圆形掩码
        center = (w//2, h//2)
        radius = min(w, h) // 4
        cv2.circle(mask, center, radius, 1, -1)
    elif shape_type == 'rectangle':
        # 创建矩形掩码
        x1, y1 = w//4, h//4
        x2, y2 = 3*w//4, 3*h//4
        cv2.rectangle(mask, (x1, y1), (x2, y2), 1, -1)
    elif shape_type == 'complex':
        # 创建复杂形状
        center = (w//2, h//2)
        radius = min(w, h) // 4
        cv2.circle(mask, center, radius, 1, -1)
        # 添加一个小的突出部分
        cv2.circle(mask, (center[0] + radius//2, center[1]), radius//3, 1, -1)
    
    return mask.astype(np.float32)

def test_point_sampling():
    """
    测试点提示采样功能
    """
    print("🧪 开始测试动态点提示采样功能...")
    
    # 导入点提示采样函数
    try:
        from train_larch_sam import (
            sample_positive_points, 
            sample_negative_points, 
            compute_center_point,
            dynamic_point_sampling
        )
        print("✅ 成功导入点提示采样函数")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 测试不同形状的掩码
    shapes = ['circle', 'rectangle', 'complex']
    
    for shape_type in shapes:
        print(f"\n📊 测试形状: {shape_type}")
        
        # 创建测试掩码
        mask_np = create_test_mask(size=(256, 256), shape_type=shape_type)
        mask_tensor = torch.from_numpy(mask_np).to(device)
        
        # 生成FIDT图
        fidt_map_np = generate_fidt_map(mask_np)
        fidt_map_tensor = torch.from_numpy(fidt_map_np).to(device)
        
        print(f"  掩码形状: {mask_tensor.shape}")
        print(f"  FIDT图形状: {fidt_map_tensor.shape}")
        print(f"  FIDT图数值范围: [{fidt_map_tensor.min():.4f}, {fidt_map_tensor.max():.4f}]")
        
        # 测试训练模式的动态采样
        print("  🎯 测试训练模式采样...")
        try:
            train_coords, train_labels = dynamic_point_sampling(
                fidt_map=fidt_map_tensor,
                target_mask=mask_tensor,
                is_training=True,
                device=device
            )
            print(f"    训练模式点数: {len(train_coords)}")
            print(f"    正样本点数: {torch.sum(train_labels == 1).item()}")
            print(f"    负样本点数: {torch.sum(train_labels == 0).item()}")
            print(f"    点坐标范围: x[{train_coords[:, 0].min():.1f}, {train_coords[:, 0].max():.1f}], y[{train_coords[:, 1].min():.1f}, {train_coords[:, 1].max():.1f}]")
        except Exception as e:
            print(f"    ❌ 训练模式采样失败: {e}")
        
        # 测试测试模式的确定性采样
        print("  🎯 测试测试模式采样...")
        try:
            test_coords, test_labels = dynamic_point_sampling(
                fidt_map=fidt_map_tensor,
                target_mask=mask_tensor,
                is_training=False,
                device=device
            )
            print(f"    测试模式点数: {len(test_coords)}")
            print(f"    点标签: {test_labels.tolist()}")
            print(f"    中心点坐标: ({test_coords[0, 0]:.1f}, {test_coords[0, 1]:.1f})")
            
            # 验证确定性：多次调用应该得到相同结果
            test_coords2, test_labels2 = dynamic_point_sampling(
                fidt_map=fidt_map_tensor,
                target_mask=mask_tensor,
                is_training=False,
                device=device
            )
            is_deterministic = torch.allclose(test_coords, test_coords2) and torch.equal(test_labels, test_labels2)
            print(f"    确定性检查: {'✅ 通过' if is_deterministic else '❌ 失败'}")
            
        except Exception as e:
            print(f"    ❌ 测试模式采样失败: {e}")
        
        # 可视化结果（可选）
        if shape_type == 'circle':  # 只为第一个形状创建可视化
            try:
                create_visualization(mask_np, fidt_map_np, train_coords, train_labels, shape_type)
                print(f"    📊 可视化已保存: test_point_sampling_{shape_type}.png")
            except Exception as e:
                print(f"    ⚠️ 可视化失败: {e}")

def create_visualization(mask, fidt_map, points, labels, shape_type):
    """
    创建可视化图像
    """
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # 原始掩码
    axes[0].imshow(mask, cmap='gray')
    axes[0].set_title('Original Mask')
    axes[0].axis('off')
    
    # FIDT图
    im1 = axes[1].imshow(fidt_map, cmap='hot')
    axes[1].set_title('FIDT Map')
    axes[1].axis('off')
    plt.colorbar(im1, ax=axes[1])
    
    # 采样点叠加在FIDT图上
    axes[2].imshow(fidt_map, cmap='hot', alpha=0.7)
    
    # 转换点坐标到numpy（如果是tensor）
    if hasattr(points, 'cpu'):
        points_np = points.cpu().numpy()
        labels_np = labels.cpu().numpy()
    else:
        points_np = points
        labels_np = labels
    
    # 绘制采样点
    pos_points = points_np[labels_np == 1]
    neg_points = points_np[labels_np == 0]
    
    if len(pos_points) > 0:
        axes[2].scatter(pos_points[:, 0], pos_points[:, 1], c='green', s=100, marker='+', linewidths=3, label='Positive')
    if len(neg_points) > 0:
        axes[2].scatter(neg_points[:, 0], neg_points[:, 1], c='red', s=100, marker='x', linewidths=3, label='Negative')
    
    axes[2].set_title('Sampled Points on FIDT Map')
    axes[2].axis('off')
    axes[2].legend()
    
    plt.tight_layout()
    plt.savefig(f'/datadisk/SAM/test_point_sampling_{shape_type}.png', dpi=150, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='测试动态点提示采样功能')
    parser.add_argument('--visualize', action='store_true', help='创建可视化图像')
    
    args = parser.parse_args()
    
    test_point_sampling()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
