#!/bin/bash

# ==========================================
# SAM优化训练启动脚本 - 24GB GPU配置
# ==========================================

echo "🚀 启动完整SAM训练..."
echo "📋 训练配置: 生产环境优化"

# 进入项目目录
cd /datadisk/SAM

# ==========================================
# CUDA性能优化设置
# ==========================================

# 异步执行，最大化GPU利用率
export CUDA_LAUNCH_BLOCKING=0

# 启用cuDNN v8 API，提升卷积性能
export TORCH_CUDNN_V8_API_ENABLED=1

# 延迟加载CUDA模块，节省启动内存
export CUDA_MODULE_LOADING=LAZY

# 内存分配优化 - 24GB GPU适配
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024,expandable_segments:True

# ==========================================
# PyTorch性能优化
# ==========================================

# 启用TensorFloat-32 (TF32) 用于更快的混合精度训练
export TORCH_ALLOW_TF32_CUBLAS_OVERRIDE=1

# 优化内存分配策略
export PYTORCH_NO_CUDA_MEMORY_CACHING=0

# 多线程优化
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8

# ==========================================
# 启动训练
# ==========================================

# 🚀 批处理优化训练参数 - 更大的批次测试真正的并行化
# python train_larch_sam.py \
#   --data_root /datadisk/SAM/processed_larch_dataset \
#   --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
#   --model_type vit_h \
#   --output_dir /datadisk/SAM/output \
#   --loss_type combined \
#   --weight_method inverse \
#   --auc_loss_name log_loss \
#   --auc_epsilon 0.05 \
#   --auc_samples 1000 \
#   --num_epochs 20 \
#   --lr 1e-5 \
#   --lr_uncertainty 1e-2 \
#   --weight_decay 0.01 \
#   --num_workers 4 \
#   --batch_size 32 \
#   --accumulation_steps 1 \
#   --grad_clip 1.0 \
#   --use_amp \
#   --seed 42 \
#   --use_wt_enhancer \
#   --wt_enhancer_levels 1 \
#   --wt_enhancer_wavelet 'bior2.2' \
#   --wt_enhancer_kernel_size 3 \
#   --wt_enhancer_dropout 0.05 \
#   --lr_wt_enhancer 1e-3 > /datadisk/SAM/output/full_terminal_output.txt 2>&1

# python train_larch_sam.py \
#   --data_root /datadisk/SAM/processed_larch_dataset \
#   --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
#   --model_type vit_h \
#   --output_dir /datadisk/SAM/output \
#   --loss_type combined \
#   --weight_method inverse \
#   --auc_loss_name log_loss \
#   --auc_epsilon 0.05 \
#   --auc_samples 1000 \
#   --num_epochs 1 \
#   --lr 1e-5 \
#   --debug \
#   --lr_uncertainty 1e-2 \
#   --weight_decay 0.01 \
#   --num_workers 4 \
#   --batch_size 16 \
#   --accumulation_steps 1 \
#   --grad_clip 1.0 \
#   --use_amp \
#   --seed 42 \
#   --use_wt_enhancer \
#   --wt_enhancer_levels 1 \
#   --wt_enhancer_wavelet 'bior2.2' \
#   --wt_enhancer_kernel_size 3 \
#   --lr_wt_enhancer 1e-3 > /datadisk/SAM/output/debug_output.txt 2>&1

python train_larch_sam.py \
  --data_root /datadisk/SAM/processed_larch_dataset \
  --sam_checkpoint /datadisk/SAM/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --output_dir /datadisk/SAM/output \
  --loss_type combined \
  --weight_method inverse \
  --auc_loss_name log_loss \
  --auc_epsilon 0.05 \
  --auc_samples 1000 \
  --num_epochs 1 \
  --lr 1e-5 \
  --debug \
  --lr_uncertainty 1e-2 \
  --weight_decay 0.01 \
  --num_workers 4 \
  --batch_size 16 \
  --accumulation_steps 1 \
  --grad_clip 1.0 \
  --use_amp \
  --seed 42 > /datadisk/SAM/output/debug_output.txt 2>&1
